var u=Object.defineProperty;var m=(c,t,i)=>t in c?u(c,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):c[t]=i;var n=(c,t,i)=>m(c,typeof t!="symbol"?t+"":t,i);import{F as p,B as f,M as d,a as l,S as y,C as M,A as T,N as w,V as g,b as A}from"./three-CJ0eQX7f.js";class E{constructor(){n(this,"loader");this.loader=new p,this.loader.setResponseType("arraybuffer")}async loadModel(t){return new Promise((i,a)=>{this.loader.load(t,e=>{try{const s=this.parseModel(e);i(s)}catch(s){a(s)}},void 0,a)})}async loadMotion(t){return new Promise((i,a)=>{this.loader.load(t,e=>{try{const s=this.parseMotion(e);i(s)}catch(s){a(s)}},void 0,a)})}parseModel(t){const i=new f(2,4,1),a=new d({color:65416,transparent:!0,opacity:.8}),e=new l(i,a);e.position.y=2,e.castShadow=!0,e.receiveShadow=!0;const s=new y(.8),r=new d({color:16768426}),o=new l(s,r);return o.position.y=3,e.add(o),e}parseMotion(t){return{duration:10,frames:[],bones:[],morphs:[]}}async loadModelFromFile(t){return new Promise((i,a)=>{const e=new FileReader;e.onload=s=>{var r;try{const o=(r=s.target)==null?void 0:r.result,h=this.parseModel(o);i(h)}catch(o){a(o)}},e.onerror=()=>a(new Error("文件读取失败")),e.readAsArrayBuffer(t)})}async loadMotionFromFile(t){return new Promise((i,a)=>{const e=new FileReader;e.onload=s=>{var r;try{const o=(r=s.target)==null?void 0:r.result,h=this.parseMotion(o);i(h)}catch(o){a(o)}},e.onerror=()=>a(new Error("文件读取失败")),e.readAsArrayBuffer(t)})}}class x{constructor(){n(this,"clock");n(this,"mixer");n(this,"actions",[]);n(this,"audio");n(this,"isPlaying",!1);n(this,"startTime",0);this.clock=new M}add(t,i){if(!i)return;this.mixer=new T(t);const a=new w(".rotation[y]",[0,5,10],[0,Math.PI,Math.PI*2]),e=new g(".position",[0,5,10],[0,0,0,0,2,0,0,0,0]),s=new A("motion",10,[a,e]),r=this.mixer.clipAction(s);this.actions.push(r)}setAudio(t){this.audio=t}play(){this.isPlaying||(this.isPlaying=!0,this.startTime=this.clock.getElapsedTime(),this.actions.forEach(t=>{t.play()}),this.audio&&this.audio.play())}pause(){this.isPlaying=!1,this.actions.forEach(t=>{t.paused=!0}),this.audio&&this.audio.pause()}stop(){this.isPlaying=!1,this.actions.forEach(t=>{t.stop()}),this.audio&&(this.audio.pause(),this.audio.currentTime=0),this.startTime=0}seek(t){this.actions.forEach(i=>{i.time=t}),this.audio&&(this.audio.currentTime=t),this.isPlaying&&(this.startTime=this.clock.getElapsedTime()-t)}update(){if(!this.mixer||!this.isPlaying)return;const t=this.clock.getDelta();this.mixer.update(t),this.syncAudioAndAnimation()}syncAudioAndAnimation(){if(!this.audio)return;const t=this.clock.getElapsedTime()-this.startTime,i=this.audio.currentTime;Math.abs(t-i)>.1&&this.actions.forEach(e=>{e.time=i})}getCurrentTime(){return this.isPlaying?this.clock.getElapsedTime()-this.startTime:0}getDuration(){return this.actions.length===0?0:Math.max(...this.actions.map(t=>t.getClip().duration))}setTimeScale(t){this.actions.forEach(i=>{i.setEffectiveTimeScale(t)}),this.audio&&(this.audio.playbackRate=t)}dispose(){this.stop(),this.actions=[],this.mixer=void 0,this.audio=void 0}}export{E as M,x as a};
