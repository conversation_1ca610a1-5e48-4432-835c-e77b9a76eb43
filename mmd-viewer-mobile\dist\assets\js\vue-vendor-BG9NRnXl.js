/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function vs(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const te={},Pt=[],gt=()=>{},gi=()=>!1,An=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),bs=e=>e.startsWith("onUpdate:"),Pe=Object.assign,Ur=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},mi=Object.prototype.hasOwnProperty,Y=(e,t)=>mi.call(e,t),B=Array.isArray,Ot=e=>on(e)==="[object Map]",Pn=e=>on(e)==="[object Set]",qs=e=>on(e)==="[object Date]",Z=e=>typeof e=="function",fe=e=>typeof e=="string",Be=e=>typeof e=="symbol",oe=e=>e!==null&&typeof e=="object",Wr=e=>(oe(e)||Z(e))&&Z(e.then)&&Z(e.catch),qr=Object.prototype.toString,on=e=>qr.call(e),yi=e=>on(e).slice(8,-1),Gr=e=>on(e)==="[object Object]",Ss=e=>fe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Bt=vs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),On=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},_i=/-(\w)/g,Ae=On(e=>e.replace(_i,(t,n)=>n?n.toUpperCase():"")),vi=/\B([A-Z])/g,St=On(e=>e.replace(vi,"-$1").toLowerCase()),In=On(e=>e.charAt(0).toUpperCase()+e.slice(1)),Kn=On(e=>e?`on${In(e)}`:""),nt=(e,t)=>!Object.is(e,t),dn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},zr=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},yn=e=>{const t=parseFloat(e);return isNaN(t)?e:t},bi=e=>{const t=fe(e)?Number(e):NaN;return isNaN(t)?e:t};let Gs;const Mn=()=>Gs||(Gs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Es(e){if(B(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=fe(s)?wi(s):Es(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(fe(e)||oe(e))return e}const Si=/;(?![^(]*\))/g,Ei=/:([^]+)/,Ci=/\/\*[^]*?\*\//g;function wi(e){const t={};return e.replace(Ci,"").split(Si).forEach(n=>{if(n){const s=n.split(Ei);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Cs(e){let t="";if(fe(e))t=e;else if(B(e))for(let n=0;n<e.length;n++){const s=Cs(e[n]);s&&(t+=s+" ")}else if(oe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const xi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ri=vs(xi);function Jr(e){return!!e||e===""}function Ti(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Ln(e[s],t[s]);return n}function Ln(e,t){if(e===t)return!0;let n=qs(e),s=qs(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=Be(e),s=Be(t),n||s)return e===t;if(n=B(e),s=B(t),n||s)return n&&s?Ti(e,t):!1;if(n=oe(e),s=oe(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),c=t.hasOwnProperty(i);if(l&&!c||!l&&c||!Ln(e[i],t[i]))return!1}}return String(e)===String(t)}function Ai(e,t){return e.findIndex(n=>Ln(n,t))}const Qr=e=>!!(e&&e.__v_isRef===!0),Pi=e=>fe(e)?e:e==null?"":B(e)||oe(e)&&(e.toString===qr||!Z(e.toString))?Qr(e)?Pi(e.value):JSON.stringify(e,Yr,2):String(e),Yr=(e,t)=>Qr(t)?Yr(e,t.value):Ot(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[Un(s,o)+" =>"]=r,n),{})}:Pn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Un(n))}:Be(t)?Un(t):oe(t)&&!B(t)&&!Gr(t)?String(t):t,Un=(e,t="")=>{var n;return Be(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ue;class Xr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ue,!t&&ue&&(this.index=(ue.scopes||(ue.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ue;try{return ue=this,t()}finally{ue=n}}}on(){++this._on===1&&(this.prevScope=ue,ue=this)}off(){this._on>0&&--this._on===0&&(ue=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Zr(e){return new Xr(e)}function eo(){return ue}function Oi(e,t=!1){ue&&ue.cleanups.push(e)}let re;const Wn=new WeakSet;class to{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ue&&ue.active&&ue.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Wn.has(this)&&(Wn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||so(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,zs(this),ro(this);const t=re,n=Ie;re=this,Ie=!0;try{return this.fn()}finally{oo(this),re=t,Ie=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Rs(t);this.deps=this.depsTail=void 0,zs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Wn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){os(this)&&this.run()}get dirty(){return os(this)}}let no=0,kt,Kt;function so(e,t=!1){if(e.flags|=8,t){e.next=Kt,Kt=e;return}e.next=kt,kt=e}function ws(){no++}function xs(){if(--no>0)return;if(Kt){let t=Kt;for(Kt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;kt;){let t=kt;for(kt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function ro(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function oo(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Rs(s),Ii(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function os(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(io(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function io(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Qt)||(e.globalVersion=Qt,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!os(e))))return;e.flags|=2;const t=e.dep,n=re,s=Ie;re=e,Ie=!0;try{ro(e);const r=e.fn(e._value);(t.version===0||nt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{re=n,Ie=s,oo(e),e.flags&=-3}}function Rs(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Rs(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ii(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ie=!0;const lo=[];function st(){lo.push(Ie),Ie=!1}function rt(){const e=lo.pop();Ie=e===void 0?!0:e}function zs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=re;re=void 0;try{t()}finally{re=n}}}let Qt=0;class Mi{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ts{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!re||!Ie||re===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==re)n=this.activeLink=new Mi(re,this),re.deps?(n.prevDep=re.depsTail,re.depsTail.nextDep=n,re.depsTail=n):re.deps=re.depsTail=n,co(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=re.depsTail,n.nextDep=void 0,re.depsTail.nextDep=n,re.depsTail=n,re.deps===n&&(re.deps=s)}return n}trigger(t){this.version++,Qt++,this.notify(t)}notify(t){ws();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{xs()}}}function co(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)co(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const _n=new WeakMap,mt=Symbol(""),is=Symbol(""),Yt=Symbol("");function ae(e,t,n){if(Ie&&re){let s=_n.get(e);s||_n.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Ts),r.map=s,r.key=n),r.track()}}function Ge(e,t,n,s,r,o){const i=_n.get(e);if(!i){Qt++;return}const l=c=>{c&&c.trigger()};if(ws(),t==="clear")i.forEach(l);else{const c=B(e),d=c&&Ss(n);if(c&&n==="length"){const f=Number(s);i.forEach((h,p)=>{(p==="length"||p===Yt||!Be(p)&&p>=f)&&l(h)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),d&&l(i.get(Yt)),t){case"add":c?d&&l(i.get("length")):(l(i.get(mt)),Ot(e)&&l(i.get(is)));break;case"delete":c||(l(i.get(mt)),Ot(e)&&l(i.get(is)));break;case"set":Ot(e)&&l(i.get(mt));break}}xs()}function Li(e,t){const n=_n.get(e);return n&&n.get(t)}function xt(e){const t=q(e);return t===e?t:(ae(t,"iterate",Yt),Me(e)?t:t.map(de))}function As(e){return ae(e=q(e),"iterate",Yt),e}const Ni={__proto__:null,[Symbol.iterator](){return qn(this,Symbol.iterator,de)},concat(...e){return xt(this).concat(...e.map(t=>B(t)?xt(t):t))},entries(){return qn(this,"entries",e=>(e[1]=de(e[1]),e))},every(e,t){return Ke(this,"every",e,t,void 0,arguments)},filter(e,t){return Ke(this,"filter",e,t,n=>n.map(de),arguments)},find(e,t){return Ke(this,"find",e,t,de,arguments)},findIndex(e,t){return Ke(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ke(this,"findLast",e,t,de,arguments)},findLastIndex(e,t){return Ke(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ke(this,"forEach",e,t,void 0,arguments)},includes(...e){return Gn(this,"includes",e)},indexOf(...e){return Gn(this,"indexOf",e)},join(e){return xt(this).join(e)},lastIndexOf(...e){return Gn(this,"lastIndexOf",e)},map(e,t){return Ke(this,"map",e,t,void 0,arguments)},pop(){return Ht(this,"pop")},push(...e){return Ht(this,"push",e)},reduce(e,...t){return Js(this,"reduce",e,t)},reduceRight(e,...t){return Js(this,"reduceRight",e,t)},shift(){return Ht(this,"shift")},some(e,t){return Ke(this,"some",e,t,void 0,arguments)},splice(...e){return Ht(this,"splice",e)},toReversed(){return xt(this).toReversed()},toSorted(e){return xt(this).toSorted(e)},toSpliced(...e){return xt(this).toSpliced(...e)},unshift(...e){return Ht(this,"unshift",e)},values(){return qn(this,"values",de)}};function qn(e,t,n){const s=As(e),r=s[t]();return s!==e&&!Me(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Fi=Array.prototype;function Ke(e,t,n,s,r,o){const i=As(e),l=i!==e&&!Me(e),c=i[t];if(c!==Fi[t]){const h=c.apply(e,o);return l?de(h):h}let d=n;i!==e&&(l?d=function(h,p){return n.call(this,de(h),p,e)}:n.length>2&&(d=function(h,p){return n.call(this,h,p,e)}));const f=c.call(i,d,s);return l&&r?r(f):f}function Js(e,t,n,s){const r=As(e);let o=n;return r!==e&&(Me(e)?n.length>3&&(o=function(i,l,c){return n.call(this,i,l,c,e)}):o=function(i,l,c){return n.call(this,i,de(l),c,e)}),r[t](o,...s)}function Gn(e,t,n){const s=q(e);ae(s,"iterate",Yt);const r=s[t](...n);return(r===-1||r===!1)&&Is(n[0])?(n[0]=q(n[0]),s[t](...n)):r}function Ht(e,t,n=[]){st(),ws();const s=q(e)[t].apply(e,n);return xs(),rt(),s}const $i=vs("__proto__,__v_isRef,__isVue"),fo=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Be));function Hi(e){Be(e)||(e=String(e));const t=q(this);return ae(t,"has",e),t.hasOwnProperty(e)}class uo{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?Gi:go:o?po:ho).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=B(t);if(!r){let c;if(i&&(c=Ni[n]))return c;if(n==="hasOwnProperty")return Hi}const l=Reflect.get(t,n,le(t)?t:s);return(Be(n)?fo.has(n):$i(n))||(r||ae(t,"get",n),o)?l:le(l)?i&&Ss(n)?l:l.value:oe(l)?r?yo(l):Nn(l):l}}class ao extends uo{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=vt(o);if(!Me(s)&&!vt(s)&&(o=q(o),s=q(s)),!B(t)&&le(o)&&!le(s))return c?!1:(o.value=s,!0)}const i=B(t)&&Ss(n)?Number(n)<t.length:Y(t,n),l=Reflect.set(t,n,s,le(t)?t:r);return t===q(r)&&(i?nt(s,o)&&Ge(t,"set",n,s):Ge(t,"add",n,s)),l}deleteProperty(t,n){const s=Y(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Ge(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Be(n)||!fo.has(n))&&ae(t,"has",n),s}ownKeys(t){return ae(t,"iterate",B(t)?"length":mt),Reflect.ownKeys(t)}}class Di extends uo{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const ji=new ao,Vi=new Di,Bi=new ao(!0);const ls=e=>e,fn=e=>Reflect.getPrototypeOf(e);function ki(e,t,n){return function(...s){const r=this.__v_raw,o=q(r),i=Ot(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,d=r[e](...s),f=n?ls:t?cs:de;return!t&&ae(o,"iterate",c?is:mt),{next(){const{value:h,done:p}=d.next();return p?{value:h,done:p}:{value:l?[f(h[0]),f(h[1])]:f(h),done:p}},[Symbol.iterator](){return this}}}}function un(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Ki(e,t){const n={get(r){const o=this.__v_raw,i=q(o),l=q(r);e||(nt(r,l)&&ae(i,"get",r),ae(i,"get",l));const{has:c}=fn(i),d=t?ls:e?cs:de;if(c.call(i,r))return d(o.get(r));if(c.call(i,l))return d(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&ae(q(r),"iterate",mt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=q(o),l=q(r);return e||(nt(r,l)&&ae(i,"has",r),ae(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=q(l),d=t?ls:e?cs:de;return!e&&ae(c,"iterate",mt),l.forEach((f,h)=>r.call(o,d(f),d(h),i))}};return Pe(n,e?{add:un("add"),set:un("set"),delete:un("delete"),clear:un("clear")}:{add(r){!t&&!Me(r)&&!vt(r)&&(r=q(r));const o=q(this);return fn(o).has.call(o,r)||(o.add(r),Ge(o,"add",r,r)),this},set(r,o){!t&&!Me(o)&&!vt(o)&&(o=q(o));const i=q(this),{has:l,get:c}=fn(i);let d=l.call(i,r);d||(r=q(r),d=l.call(i,r));const f=c.call(i,r);return i.set(r,o),d?nt(o,f)&&Ge(i,"set",r,o):Ge(i,"add",r,o),this},delete(r){const o=q(this),{has:i,get:l}=fn(o);let c=i.call(o,r);c||(r=q(r),c=i.call(o,r)),l&&l.call(o,r);const d=o.delete(r);return c&&Ge(o,"delete",r,void 0),d},clear(){const r=q(this),o=r.size!==0,i=r.clear();return o&&Ge(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=ki(r,e,t)}),n}function Ps(e,t){const n=Ki(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(Y(n,r)&&r in s?n:s,r,o)}const Ui={get:Ps(!1,!1)},Wi={get:Ps(!1,!0)},qi={get:Ps(!0,!1)};const ho=new WeakMap,po=new WeakMap,go=new WeakMap,Gi=new WeakMap;function zi(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ji(e){return e.__v_skip||!Object.isExtensible(e)?0:zi(yi(e))}function Nn(e){return vt(e)?e:Os(e,!1,ji,Ui,ho)}function mo(e){return Os(e,!1,Bi,Wi,po)}function yo(e){return Os(e,!0,Vi,qi,go)}function Os(e,t,n,s,r){if(!oe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Ji(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function yt(e){return vt(e)?yt(e.__v_raw):!!(e&&e.__v_isReactive)}function vt(e){return!!(e&&e.__v_isReadonly)}function Me(e){return!!(e&&e.__v_isShallow)}function Is(e){return e?!!e.__v_raw:!1}function q(e){const t=e&&e.__v_raw;return t?q(t):e}function Ms(e){return!Y(e,"__v_skip")&&Object.isExtensible(e)&&zr(e,"__v_skip",!0),e}const de=e=>oe(e)?Nn(e):e,cs=e=>oe(e)?yo(e):e;function le(e){return e?e.__v_isRef===!0:!1}function Ls(e){return _o(e,!1)}function Qi(e){return _o(e,!0)}function _o(e,t){return le(e)?e:new Yi(e,t)}class Yi{constructor(t,n){this.dep=new Ts,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:q(t),this._value=n?t:de(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Me(t)||vt(t);t=s?t:q(t),nt(t,n)&&(this._rawValue=t,this._value=s?t:de(t),this.dep.trigger())}}function It(e){return le(e)?e.value:e}const Xi={get:(e,t,n)=>t==="__v_raw"?e:It(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return le(r)&&!le(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function vo(e){return yt(e)?e:new Proxy(e,Xi)}function Zi(e){const t=B(e)?new Array(e.length):{};for(const n in e)t[n]=tl(e,n);return t}class el{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Li(q(this._object),this._key)}}function tl(e,t,n){const s=e[t];return le(s)?s:new el(e,t,n)}class nl{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ts(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Qt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&re!==this)return so(this,!0),!0}get value(){const t=this.dep.track();return io(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function sl(e,t,n=!1){let s,r;return Z(e)?s=e:(s=e.get,r=e.set),new nl(s,r,n)}const an={},vn=new WeakMap;let ht;function rl(e,t=!1,n=ht){if(n){let s=vn.get(n);s||vn.set(n,s=[]),s.push(e)}}function ol(e,t,n=te){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=n,d=M=>r?M:Me(M)||r===!1||r===0?ze(M,1):ze(M);let f,h,p,m,C=!1,A=!1;if(le(e)?(h=()=>e.value,C=Me(e)):yt(e)?(h=()=>d(e),C=!0):B(e)?(A=!0,C=e.some(M=>yt(M)||Me(M)),h=()=>e.map(M=>{if(le(M))return M.value;if(yt(M))return d(M);if(Z(M))return c?c(M,2):M()})):Z(e)?t?h=c?()=>c(e,2):e:h=()=>{if(p){st();try{p()}finally{rt()}}const M=ht;ht=f;try{return c?c(e,3,[m]):e(m)}finally{ht=M}}:h=gt,t&&r){const M=h,k=r===!0?1/0:r;h=()=>ze(M(),k)}const j=eo(),F=()=>{f.stop(),j&&j.active&&Ur(j.effects,f)};if(o&&t){const M=t;t=(...k)=>{M(...k),F()}}let L=A?new Array(e.length).fill(an):an;const $=M=>{if(!(!(f.flags&1)||!f.dirty&&!M))if(t){const k=f.run();if(r||C||(A?k.some((ne,G)=>nt(ne,L[G])):nt(k,L))){p&&p();const ne=ht;ht=f;try{const G=[k,L===an?void 0:A&&L[0]===an?[]:L,m];L=k,c?c(t,3,G):t(...G)}finally{ht=ne}}}else f.run()};return l&&l($),f=new to(h),f.scheduler=i?()=>i($,!1):$,m=M=>rl(M,!1,f),p=f.onStop=()=>{const M=vn.get(f);if(M){if(c)c(M,4);else for(const k of M)k();vn.delete(f)}},t?s?$(!0):L=f.run():i?i($.bind(null,!0),!0):f.run(),F.pause=f.pause.bind(f),F.resume=f.resume.bind(f),F.stop=F,F}function ze(e,t=1/0,n){if(t<=0||!oe(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,le(e))ze(e.value,t,n);else if(B(e))for(let s=0;s<e.length;s++)ze(e[s],t,n);else if(Pn(e)||Ot(e))e.forEach(s=>{ze(s,t,n)});else if(Gr(e)){for(const s in e)ze(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&ze(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function ln(e,t,n,s){try{return s?e(...s):e()}catch(r){Fn(r,t,n)}}function ke(e,t,n,s){if(Z(e)){const r=ln(e,t,n,s);return r&&Wr(r)&&r.catch(o=>{Fn(o,t,n)}),r}if(B(e)){const r=[];for(let o=0;o<e.length;o++)r.push(ke(e[o],t,n,s));return r}}function Fn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||te;if(t){let l=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const f=l.ec;if(f){for(let h=0;h<f.length;h++)if(f[h](e,c,d)===!1)return}l=l.parent}if(o){st(),ln(o,null,10,[e,c,d]),rt();return}}il(e,n,r,s,i)}function il(e,t,n,s=!0,r=!1){if(r)throw e}const pe=[];let De=-1;const Mt=[];let Xe=null,Tt=0;const bo=Promise.resolve();let bn=null;function $n(e){const t=bn||bo;return e?t.then(this?e.bind(this):e):t}function ll(e){let t=De+1,n=pe.length;for(;t<n;){const s=t+n>>>1,r=pe[s],o=Xt(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function Ns(e){if(!(e.flags&1)){const t=Xt(e),n=pe[pe.length-1];!n||!(e.flags&2)&&t>=Xt(n)?pe.push(e):pe.splice(ll(t),0,e),e.flags|=1,So()}}function So(){bn||(bn=bo.then(Co))}function cl(e){B(e)?Mt.push(...e):Xe&&e.id===-1?Xe.splice(Tt+1,0,e):e.flags&1||(Mt.push(e),e.flags|=1),So()}function Qs(e,t,n=De+1){for(;n<pe.length;n++){const s=pe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;pe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Eo(e){if(Mt.length){const t=[...new Set(Mt)].sort((n,s)=>Xt(n)-Xt(s));if(Mt.length=0,Xe){Xe.push(...t);return}for(Xe=t,Tt=0;Tt<Xe.length;Tt++){const n=Xe[Tt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Xe=null,Tt=0}}const Xt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Co(e){try{for(De=0;De<pe.length;De++){const t=pe[De];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),ln(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;De<pe.length;De++){const t=pe[De];t&&(t.flags&=-2)}De=-1,pe.length=0,Eo(),bn=null,(pe.length||Mt.length)&&Co()}}let me=null,wo=null;function Sn(e){const t=me;return me=e,wo=e&&e.type.__scopeId||null,t}function fl(e,t=me,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&sr(-1);const o=Sn(t);let i;try{i=e(...r)}finally{Sn(o),s._d&&sr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function Gf(e,t){if(me===null)return e;const n=jn(me),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,c=te]=t[r];o&&(Z(o)&&(o={mounted:o,updated:o}),o.deep&&ze(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function ct(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[s];c&&(st(),ke(c,n,8,[e.el,l,e,t]),rt())}}const ul=Symbol("_vte"),xo=e=>e.__isTeleport,Ze=Symbol("_leaveCb"),hn=Symbol("_enterCb");function al(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return ml(()=>{e.isMounted=!0}),yl(()=>{e.isUnmounting=!0}),e}const Te=[Function,Array],Ro={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Te,onEnter:Te,onAfterEnter:Te,onEnterCancelled:Te,onBeforeLeave:Te,onLeave:Te,onAfterLeave:Te,onLeaveCancelled:Te,onBeforeAppear:Te,onAppear:Te,onAfterAppear:Te,onAppearCancelled:Te},To=e=>{const t=e.subTree;return t.component?To(t.component):t},hl={name:"BaseTransition",props:Ro,setup(e,{slots:t}){const n=nc(),s=al();return()=>{const r=t.default&&Oo(t.default(),!0);if(!r||!r.length)return;const o=Ao(r),i=q(e),{mode:l}=i;if(s.isLeaving)return zn(o);const c=Ys(o);if(!c)return zn(o);let d=fs(c,i,s,n,h=>d=h);c.type!==ge&&Zt(c,d);let f=n.subTree&&Ys(n.subTree);if(f&&f.type!==ge&&!dt(c,f)&&To(n).type!==ge){let h=fs(f,i,s,n);if(Zt(f,h),l==="out-in"&&c.type!==ge)return s.isLeaving=!0,h.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete h.afterLeave,f=void 0},zn(o);l==="in-out"&&c.type!==ge?h.delayLeave=(p,m,C)=>{const A=Po(s,f);A[String(f.key)]=f,p[Ze]=()=>{m(),p[Ze]=void 0,delete d.delayedLeave,f=void 0},d.delayedLeave=()=>{C(),delete d.delayedLeave,f=void 0}}:f=void 0}else f&&(f=void 0);return o}}};function Ao(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==ge){t=n;break}}return t}const dl=hl;function Po(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function fs(e,t,n,s,r){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:c,onEnter:d,onAfterEnter:f,onEnterCancelled:h,onBeforeLeave:p,onLeave:m,onAfterLeave:C,onLeaveCancelled:A,onBeforeAppear:j,onAppear:F,onAfterAppear:L,onAppearCancelled:$}=t,M=String(e.key),k=Po(n,e),ne=(P,U)=>{P&&ke(P,s,9,U)},G=(P,U)=>{const J=U[1];ne(P,U),B(P)?P.every(I=>I.length<=1)&&J():P.length<=1&&J()},K={mode:i,persisted:l,beforeEnter(P){let U=c;if(!n.isMounted)if(o)U=j||c;else return;P[Ze]&&P[Ze](!0);const J=k[M];J&&dt(e,J)&&J.el[Ze]&&J.el[Ze](),ne(U,[P])},enter(P){let U=d,J=f,I=h;if(!n.isMounted)if(o)U=F||d,J=L||f,I=$||h;else return;let z=!1;const ce=P[hn]=ye=>{z||(z=!0,ye?ne(I,[P]):ne(J,[P]),K.delayedLeave&&K.delayedLeave(),P[hn]=void 0)};U?G(U,[P,ce]):ce()},leave(P,U){const J=String(e.key);if(P[hn]&&P[hn](!0),n.isUnmounting)return U();ne(p,[P]);let I=!1;const z=P[Ze]=ce=>{I||(I=!0,U(),ce?ne(A,[P]):ne(C,[P]),P[Ze]=void 0,k[J]===e&&delete k[J])};k[J]=e,m?G(m,[P,z]):z()},clone(P){const U=fs(P,t,n,s,r);return r&&r(U),U}};return K}function zn(e){if(Fs(e))return e=ot(e),e.children=null,e}function Ys(e){if(!Fs(e))return xo(e.type)&&e.children?Ao(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&Z(n.default))return n.default()}}function Zt(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Zt(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Oo(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===je?(i.patchFlag&128&&r++,s=s.concat(Oo(i.children,t,l))):(t||i.type!==ge)&&s.push(l!=null?ot(i,{key:l}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Io(e,t){return Z(e)?Pe({name:e.name},t,{setup:e}):e}function pl(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function En(e,t,n,s,r=!1){if(B(e)){e.forEach((C,A)=>En(C,t&&(B(t)?t[A]:t),n,s,r));return}if(Ut(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&En(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?jn(s.component):s.el,i=r?null:o,{i:l,r:c}=e,d=t&&t.r,f=l.refs===te?l.refs={}:l.refs,h=l.setupState,p=q(h),m=h===te?()=>!1:C=>Y(p,C);if(d!=null&&d!==c&&(fe(d)?(f[d]=null,m(d)&&(h[d]=null)):le(d)&&(d.value=null)),Z(c))ln(c,l,12,[i,f]);else{const C=fe(c),A=le(c);if(C||A){const j=()=>{if(e.f){const F=C?m(c)?h[c]:f[c]:c.value;r?B(F)&&Ur(F,o):B(F)?F.includes(o)||F.push(o):C?(f[c]=[o],m(c)&&(h[c]=f[c])):(c.value=[o],e.k&&(f[e.k]=c.value))}else C?(f[c]=i,m(c)&&(h[c]=i)):A&&(c.value=i,e.k&&(f[e.k]=i))};i?(j.id=-1,Ce(j,n)):j()}}}Mn().requestIdleCallback;Mn().cancelIdleCallback;const Ut=e=>!!e.type.__asyncLoader,Fs=e=>e.type.__isKeepAlive;function gl(e,t,n=he,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{st();const l=Vs(n),c=ke(t,n,e,i);return l(),rt(),c});return s?r.unshift(o):r.push(o),o}}const $s=e=>(t,n=he)=>{(!tn||e==="sp")&&gl(e,(...s)=>t(...s),n)},ml=$s("m"),yl=$s("bum"),zf=$s("um"),_l="components";function Jf(e,t){return bl(_l,e,!0,t)||e}const vl=Symbol.for("v-ndc");function bl(e,t,n=!0,s=!1){const r=me||he;if(r){const o=r.type;{const l=lc(o,!1);if(l&&(l===t||l===Ae(t)||l===In(Ae(t))))return o}const i=Xs(r[e]||o[e],t)||Xs(r.appContext[e],t);return!i&&s?o:i}}function Xs(e,t){return e&&(e[t]||e[Ae(t)]||e[In(Ae(t))])}const us=e=>e?Go(e)?jn(e):us(e.parent):null,Wt=Pe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>us(e.parent),$root:e=>us(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>e.type,$forceUpdate:e=>e.f||(e.f=()=>{Ns(e.update)}),$nextTick:e=>e.n||(e.n=$n.bind(e.proxy)),$watch:e=>gt}),Jn=(e,t)=>e!==te&&!e.__isScriptSetup&&Y(e,t),Sl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let d;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Jn(s,t))return i[t]=1,s[t];if(r!==te&&Y(r,t))return i[t]=2,r[t];if((d=e.propsOptions[0])&&Y(d,t))return i[t]=3,o[t];if(n!==te&&Y(n,t))return i[t]=4,n[t];i[t]=0}}const f=Wt[t];let h,p;if(f)return t==="$attrs"&&ae(e.attrs,"get",""),f(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==te&&Y(n,t))return i[t]=4,n[t];if(p=c.config.globalProperties,Y(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Jn(r,t)?(r[t]=n,!0):s!==te&&Y(s,t)?(s[t]=n,!0):Y(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==te&&Y(e,i)||Jn(t,i)||(l=o[0])&&Y(l,i)||Y(s,i)||Y(Wt,i)||Y(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Y(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Mo(){return{app:null,config:{isNativeTag:gi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let El=0;function Cl(e,t){return function(s,r=null){Z(s)||(s=Pe({},s)),r!=null&&!oe(r)&&(r=null);const o=Mo(),i=new WeakSet,l=[];let c=!1;const d=o.app={_uid:El++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:fc,get config(){return o.config},set config(f){},use(f,...h){return i.has(f)||(f&&Z(f.install)?(i.add(f),f.install(d,...h)):Z(f)&&(i.add(f),f(d,...h))),d},mixin(f){return d},component(f,h){return h?(o.components[f]=h,d):o.components[f]},directive(f,h){return h?(o.directives[f]=h,d):o.directives[f]},mount(f,h,p){if(!c){const m=d._ceVNode||be(s,r);return m.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(m,f,p),c=!0,d._container=f,f.__vue_app__=d,jn(m.component)}},onUnmount(f){l.push(f)},unmount(){c&&(ke(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(f,h){return o.provides[f]=h,d},runWithContext(f){const h=_t;_t=d;try{return f()}finally{_t=h}}};return d}}let _t=null;function Qn(e,t){if(he){let n=he.provides;const s=he.parent&&he.parent.provides;s===n&&(n=he.provides=Object.create(s)),n[e]=t}}function bt(e,t,n=!1){const s=he||me;if(s||_t){let r=_t?_t._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&Z(t)?t.call(s&&s.proxy):t}}function wl(){return!!(he||me||_t)}const Lo={},No=()=>Object.create(Lo),Fo=e=>Object.getPrototypeOf(e)===Lo;function xl(e,t,n,s=!1){const r={},o=No();e.propsDefaults=Object.create(null),$o(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:mo(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Rl(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=q(r),[c]=e.propsOptions;let d=!1;if((s||i>0)&&!(i&16)){if(i&8){const f=e.vnode.dynamicProps;for(let h=0;h<f.length;h++){let p=f[h];if(Hn(e.emitsOptions,p))continue;const m=t[p];if(c)if(Y(o,p))m!==o[p]&&(o[p]=m,d=!0);else{const C=Ae(p);r[C]=as(c,l,C,m,e,!1)}else m!==o[p]&&(o[p]=m,d=!0)}}}else{$o(e,t,r,o)&&(d=!0);let f;for(const h in l)(!t||!Y(t,h)&&((f=St(h))===h||!Y(t,f)))&&(c?n&&(n[h]!==void 0||n[f]!==void 0)&&(r[h]=as(c,l,h,void 0,e,!0)):delete r[h]);if(o!==l)for(const h in o)(!t||!Y(t,h))&&(delete o[h],d=!0)}d&&Ge(e.attrs,"set","")}function $o(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(Bt(c))continue;const d=t[c];let f;r&&Y(r,f=Ae(c))?!o||!o.includes(f)?n[f]=d:(l||(l={}))[f]=d:Hn(e.emitsOptions,c)||(!(c in s)||d!==s[c])&&(s[c]=d,i=!0)}if(o){const c=q(n),d=l||te;for(let f=0;f<o.length;f++){const h=o[f];n[h]=as(r,c,h,d[h],e,!Y(d,h))}}return i}function as(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=Y(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&Z(c)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const f=Vs(r);s=d[n]=c.call(null,t),f()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===St(n))&&(s=!0))}return s}function Tl(e,t,n=!1){const s=t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];if(!o)return oe(e)&&s.set(e,Pt),Pt;if(B(o))for(let d=0;d<o.length;d++){const f=Ae(o[d]);Zs(f)&&(i[f]=te)}else if(o)for(const d in o){const f=Ae(d);if(Zs(f)){const h=o[d],p=i[f]=B(h)||Z(h)?{type:h}:Pe({},h),m=p.type;let C=!1,A=!0;if(B(m))for(let j=0;j<m.length;++j){const F=m[j],L=Z(F)&&F.name;if(L==="Boolean"){C=!0;break}else L==="String"&&(A=!1)}else C=Z(m)&&m.name==="Boolean";p[0]=C,p[1]=A,(C||Y(p,"default"))&&l.push(f)}}const c=[i,l];return oe(e)&&s.set(e,c),c}function Zs(e){return e[0]!=="$"&&!Bt(e)}const Hs=e=>e[0]==="_"||e==="$stable",Ds=e=>B(e)?e.map(Ve):[Ve(e)],Al=(e,t,n)=>{if(t._n)return t;const s=fl((...r)=>Ds(t(...r)),n);return s._c=!1,s},Ho=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Hs(r))continue;const o=e[r];if(Z(o))t[r]=Al(r,o,s);else if(o!=null){const i=Ds(o);t[r]=()=>i}}},Do=(e,t)=>{const n=Ds(t);e.slots.default=()=>n},jo=(e,t,n)=>{for(const s in t)(n||!Hs(s))&&(e[s]=t[s])},Pl=(e,t,n)=>{const s=e.slots=No();if(e.vnode.shapeFlag&32){const r=t._;r?(jo(s,t,n),n&&zr(s,"_",r,!0)):Ho(t,s)}else t&&Do(e,t)},Ol=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=te;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:jo(r,t,n):(o=!t.$stable,Ho(t,r)),i=t}else t&&(Do(e,t),i={default:1});if(o)for(const l in r)!Hs(l)&&i[l]==null&&delete r[l]},Ce=Wl;function Il(e){return Ml(e)}function Ml(e,t){const n=Mn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:d,setElementText:f,parentNode:h,nextSibling:p,setScopeId:m=gt,insertStaticContent:C}=e,A=(u,a,g,y=null,b=null,v=null,x=void 0,w=null,E=!!a.dynamicChildren)=>{if(u===a)return;u&&!dt(u,a)&&(y=_(u),xe(u,b,v,!0),u=null),a.patchFlag===-2&&(E=!1,a.dynamicChildren=null);const{type:S,ref:D,shapeFlag:T}=a;switch(S){case Dn:j(u,a,g,y);break;case ge:F(u,a,g,y);break;case pn:u==null&&L(a,g,y,x);break;case je:I(u,a,g,y,b,v,x,w,E);break;default:T&1?k(u,a,g,y,b,v,x,w,E):T&6?z(u,a,g,y,b,v,x,w,E):(T&64||T&128)&&S.process(u,a,g,y,b,v,x,w,E,N)}D!=null&&b&&En(D,u&&u.ref,v,a||u,!a)},j=(u,a,g,y)=>{if(u==null)s(a.el=l(a.children),g,y);else{const b=a.el=u.el;a.children!==u.children&&d(b,a.children)}},F=(u,a,g,y)=>{u==null?s(a.el=c(a.children||""),g,y):a.el=u.el},L=(u,a,g,y)=>{[u.el,u.anchor]=C(u.children,a,g,y,u.el,u.anchor)},$=({el:u,anchor:a},g,y)=>{let b;for(;u&&u!==a;)b=p(u),s(u,g,y),u=b;s(a,g,y)},M=({el:u,anchor:a})=>{let g;for(;u&&u!==a;)g=p(u),r(u),u=g;r(a)},k=(u,a,g,y,b,v,x,w,E)=>{a.type==="svg"?x="svg":a.type==="math"&&(x="mathml"),u==null?ne(a,g,y,b,v,x,w,E):P(u,a,b,v,x,w,E)},ne=(u,a,g,y,b,v,x,w)=>{let E,S;const{props:D,shapeFlag:T,transition:H,dirs:V}=u;if(E=u.el=i(u.type,v,D&&D.is,D),T&8?f(E,u.children):T&16&&K(u.children,E,null,y,b,Yn(u,v),x,w),V&&ct(u,null,y,"created"),G(E,u,u.scopeId,x,y),D){for(const se in D)se!=="value"&&!Bt(se)&&o(E,se,null,D[se],v,y);"value"in D&&o(E,"value",null,D.value,v),(S=D.onVnodeBeforeMount)&&He(S,y,u)}V&&ct(u,null,y,"beforeMount");const W=Ll(b,H);W&&H.beforeEnter(E),s(E,a,g),((S=D&&D.onVnodeMounted)||W||V)&&Ce(()=>{S&&He(S,y,u),W&&H.enter(E),V&&ct(u,null,y,"mounted")},b)},G=(u,a,g,y,b)=>{if(g&&m(u,g),y)for(let v=0;v<y.length;v++)m(u,y[v]);if(b){let v=b.subTree;if(a===v||Ko(v.type)&&(v.ssContent===a||v.ssFallback===a)){const x=b.vnode;G(u,x,x.scopeId,x.slotScopeIds,b.parent)}}},K=(u,a,g,y,b,v,x,w,E=0)=>{for(let S=E;S<u.length;S++){const D=u[S]=w?et(u[S]):Ve(u[S]);A(null,D,a,g,y,b,v,x,w)}},P=(u,a,g,y,b,v,x)=>{const w=a.el=u.el;let{patchFlag:E,dynamicChildren:S,dirs:D}=a;E|=u.patchFlag&16;const T=u.props||te,H=a.props||te;let V;if(g&&ft(g,!1),(V=H.onVnodeBeforeUpdate)&&He(V,g,a,u),D&&ct(a,u,g,"beforeUpdate"),g&&ft(g,!0),(T.innerHTML&&H.innerHTML==null||T.textContent&&H.textContent==null)&&f(w,""),S?U(u.dynamicChildren,S,w,g,y,Yn(a,b),v):x||it(u,a,w,null,g,y,Yn(a,b),v,!1),E>0){if(E&16)J(w,T,H,g,b);else if(E&2&&T.class!==H.class&&o(w,"class",null,H.class,b),E&4&&o(w,"style",T.style,H.style,b),E&8){const W=a.dynamicProps;for(let se=0;se<W.length;se++){const X=W[se],Se=T[X],ve=H[X];(ve!==Se||X==="value")&&o(w,X,Se,ve,b,g)}}E&1&&u.children!==a.children&&f(w,a.children)}else!x&&S==null&&J(w,T,H,g,b);((V=H.onVnodeUpdated)||D)&&Ce(()=>{V&&He(V,g,a,u),D&&ct(a,u,g,"updated")},y)},U=(u,a,g,y,b,v,x)=>{for(let w=0;w<a.length;w++){const E=u[w],S=a[w],D=E.el&&(E.type===je||!dt(E,S)||E.shapeFlag&198)?h(E.el):g;A(E,S,D,null,y,b,v,x,!0)}},J=(u,a,g,y,b)=>{if(a!==g){if(a!==te)for(const v in a)!Bt(v)&&!(v in g)&&o(u,v,a[v],null,b,y);for(const v in g){if(Bt(v))continue;const x=g[v],w=a[v];x!==w&&v!=="value"&&o(u,v,w,x,b,y)}"value"in g&&o(u,"value",a.value,g.value,b)}},I=(u,a,g,y,b,v,x,w,E)=>{const S=a.el=u?u.el:l(""),D=a.anchor=u?u.anchor:l("");let{patchFlag:T,dynamicChildren:H,slotScopeIds:V}=a;V&&(w=w?w.concat(V):V),u==null?(s(S,g,y),s(D,g,y),K(a.children||[],g,D,b,v,x,w,E)):T>0&&T&64&&H&&u.dynamicChildren?(U(u.dynamicChildren,H,g,b,v,x,w),(a.key!=null||b&&a===b.subTree)&&Vo(u,a,!0)):it(u,a,g,D,b,v,x,w,E)},z=(u,a,g,y,b,v,x,w,E)=>{a.slotScopeIds=w,u==null?a.shapeFlag&512?b.ctx.activate(a,g,y,x,E):ce(a,g,y,b,v,x,E):ye(u,a,E)},ce=(u,a,g,y,b,v,x)=>{const w=u.component=tc(u,y,b);if(Fs(u)&&(w.ctx.renderer=N),sc(w,!1,x),w.asyncDep){if(b&&b.registerDep(w,_e,x),!u.el){const E=w.subTree=be(ge);F(null,E,a,g)}}else _e(w,u,a,g,b,v,x)},ye=(u,a,g)=>{const y=a.component=u.component;if(Kl(u,a,g))if(y.asyncDep&&!y.asyncResolved){ie(y,a,g);return}else y.next=a,y.update();else a.el=u.el,y.vnode=a},_e=(u,a,g,y,b,v,x)=>{const w=()=>{if(u.isMounted){let{next:T,bu:H,u:V,parent:W,vnode:se}=u;{const Fe=Bo(u);if(Fe){T&&(T.el=se.el,ie(u,T,x)),Fe.asyncDep.then(()=>{u.isUnmounted||w()});return}}let X=T,Se;ft(u,!1),T?(T.el=se.el,ie(u,T,x)):T=se,H&&dn(H),(Se=T.props&&T.props.onVnodeBeforeUpdate)&&He(Se,W,T,se),ft(u,!0);const ve=tr(u),Ne=u.subTree;u.subTree=ve,A(Ne,ve,h(Ne.el),_(Ne),u,b,v),T.el=ve.el,X===null&&Ul(u,ve.el),V&&Ce(V,b),(Se=T.props&&T.props.onVnodeUpdated)&&Ce(()=>He(Se,W,T,se),b)}else{let T;const{el:H,props:V}=a,{bm:W,m:se,parent:X,root:Se,type:ve}=u,Ne=Ut(a);ft(u,!1),W&&dn(W),!Ne&&(T=V&&V.onVnodeBeforeMount)&&He(T,X,a),ft(u,!0);{Se.ce&&Se.ce._injectChildStyle(ve);const Fe=u.subTree=tr(u);A(null,Fe,g,y,u,b,v),a.el=Fe.el}if(se&&Ce(se,b),!Ne&&(T=V&&V.onVnodeMounted)){const Fe=a;Ce(()=>He(T,X,Fe),b)}(a.shapeFlag&256||X&&Ut(X.vnode)&&X.vnode.shapeFlag&256)&&u.a&&Ce(u.a,b),u.isMounted=!0,a=g=y=null}};u.scope.on();const E=u.effect=new to(w);u.scope.off();const S=u.update=E.run.bind(E),D=u.job=E.runIfDirty.bind(E);D.i=u,D.id=u.uid,E.scheduler=()=>Ns(D),ft(u,!0),S()},ie=(u,a,g)=>{a.component=u;const y=u.vnode.props;u.vnode=a,u.next=null,Rl(u,a.props,y,g),Ol(u,a.children,g),st(),Qs(u),rt()},it=(u,a,g,y,b,v,x,w,E=!1)=>{const S=u&&u.children,D=u?u.shapeFlag:0,T=a.children,{patchFlag:H,shapeFlag:V}=a;if(H>0){if(H&128){Et(S,T,g,y,b,v,x,w,E);return}else if(H&256){kn(S,T,g,y,b,v,x,w,E);return}}V&8?(D&16&&Re(S,b,v),T!==S&&f(g,T)):D&16?V&16?Et(S,T,g,y,b,v,x,w,E):Re(S,b,v,!0):(D&8&&f(g,""),V&16&&K(T,g,y,b,v,x,w,E))},kn=(u,a,g,y,b,v,x,w,E)=>{u=u||Pt,a=a||Pt;const S=u.length,D=a.length,T=Math.min(S,D);let H;for(H=0;H<T;H++){const V=a[H]=E?et(a[H]):Ve(a[H]);A(u[H],V,g,null,b,v,x,w,E)}S>D?Re(u,b,v,!0,!1,T):K(a,g,y,b,v,x,w,E,T)},Et=(u,a,g,y,b,v,x,w,E)=>{let S=0;const D=a.length;let T=u.length-1,H=D-1;for(;S<=T&&S<=H;){const V=u[S],W=a[S]=E?et(a[S]):Ve(a[S]);if(dt(V,W))A(V,W,g,null,b,v,x,w,E);else break;S++}for(;S<=T&&S<=H;){const V=u[T],W=a[H]=E?et(a[H]):Ve(a[H]);if(dt(V,W))A(V,W,g,null,b,v,x,w,E);else break;T--,H--}if(S>T){if(S<=H){const V=H+1,W=V<D?a[V].el:y;for(;S<=H;)A(null,a[S]=E?et(a[S]):Ve(a[S]),g,W,b,v,x,w,E),S++}}else if(S>H)for(;S<=T;)xe(u[S],b,v,!0),S++;else{const V=S,W=S,se=new Map;for(S=W;S<=H;S++){const Ee=a[S]=E?et(a[S]):Ve(a[S]);Ee.key!=null&&se.set(Ee.key,S)}let X,Se=0;const ve=H-W+1;let Ne=!1,Fe=0;const $t=new Array(ve);for(S=0;S<ve;S++)$t[S]=0;for(S=V;S<=T;S++){const Ee=u[S];if(Se>=ve){xe(Ee,b,v,!0);continue}let $e;if(Ee.key!=null)$e=se.get(Ee.key);else for(X=W;X<=H;X++)if($t[X-W]===0&&dt(Ee,a[X])){$e=X;break}$e===void 0?xe(Ee,b,v,!0):($t[$e-W]=S+1,$e>=Fe?Fe=$e:Ne=!0,A(Ee,a[$e],g,null,b,v,x,w,E),Se++)}const Us=Ne?Nl($t):Pt;for(X=Us.length-1,S=ve-1;S>=0;S--){const Ee=W+S,$e=a[Ee],Ws=Ee+1<D?a[Ee+1].el:y;$t[S]===0?A(null,$e,g,Ws,b,v,x,w,E):Ne&&(X<0||S!==Us[X]?lt($e,g,Ws,2):X--)}}},lt=(u,a,g,y,b=null)=>{const{el:v,type:x,transition:w,children:E,shapeFlag:S}=u;if(S&6){lt(u.component.subTree,a,g,y);return}if(S&128){u.suspense.move(a,g,y);return}if(S&64){x.move(u,a,g,N);return}if(x===je){s(v,a,g);for(let T=0;T<E.length;T++)lt(E[T],a,g,y);s(u.anchor,a,g);return}if(x===pn){$(u,a,g);return}if(y!==2&&S&1&&w)if(y===0)w.beforeEnter(v),s(v,a,g),Ce(()=>w.enter(v),b);else{const{leave:T,delayLeave:H,afterLeave:V}=w,W=()=>{u.ctx.isUnmounted?r(v):s(v,a,g)},se=()=>{T(v,()=>{W(),V&&V()})};H?H(v,W,se):se()}else s(v,a,g)},xe=(u,a,g,y=!1,b=!1)=>{const{type:v,props:x,ref:w,children:E,dynamicChildren:S,shapeFlag:D,patchFlag:T,dirs:H,cacheIndex:V}=u;if(T===-2&&(b=!1),w!=null&&(st(),En(w,null,g,u,!0),rt()),V!=null&&(a.renderCache[V]=void 0),D&256){a.ctx.deactivate(u);return}const W=D&1&&H,se=!Ut(u);let X;if(se&&(X=x&&x.onVnodeBeforeUnmount)&&He(X,a,u),D&6)cn(u.component,g,y);else{if(D&128){u.suspense.unmount(g,y);return}W&&ct(u,null,a,"beforeUnmount"),D&64?u.type.remove(u,a,g,N,y):S&&!S.hasOnce&&(v!==je||T>0&&T&64)?Re(S,a,g,!1,!0):(v===je&&T&384||!b&&D&16)&&Re(E,a,g),y&&Ct(u)}(se&&(X=x&&x.onVnodeUnmounted)||W)&&Ce(()=>{X&&He(X,a,u),W&&ct(u,null,a,"unmounted")},g)},Ct=u=>{const{type:a,el:g,anchor:y,transition:b}=u;if(a===je){wt(g,y);return}if(a===pn){M(u);return}const v=()=>{r(g),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(u.shapeFlag&1&&b&&!b.persisted){const{leave:x,delayLeave:w}=b,E=()=>x(g,v);w?w(u.el,v,E):E()}else v()},wt=(u,a)=>{let g;for(;u!==a;)g=p(u),r(u),u=g;r(a)},cn=(u,a,g)=>{const{bum:y,scope:b,job:v,subTree:x,um:w,m:E,a:S,parent:D,slots:{__:T}}=u;er(E),er(S),y&&dn(y),D&&B(T)&&T.forEach(H=>{D.renderCache[H]=void 0}),b.stop(),v&&(v.flags|=8,xe(x,u,a,g)),w&&Ce(w,a),Ce(()=>{u.isUnmounted=!0},a),a&&a.pendingBranch&&!a.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===a.pendingId&&(a.deps--,a.deps===0&&a.resolve())},Re=(u,a,g,y=!1,b=!1,v=0)=>{for(let x=v;x<u.length;x++)xe(u[x],a,g,y,b)},_=u=>{if(u.shapeFlag&6)return _(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const a=p(u.anchor||u.el),g=a&&a[ul];return g?p(g):a};let O=!1;const R=(u,a,g)=>{u==null?a._vnode&&xe(a._vnode,null,null,!0):A(a._vnode||null,u,a,null,null,null,g),a._vnode=u,O||(O=!0,Qs(),Eo(),O=!1)},N={p:A,um:xe,m:lt,r:Ct,mt:ce,mc:K,pc:it,pbc:U,n:_,o:e};return{render:R,hydrate:void 0,createApp:Cl(R)}}function Yn({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ft({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ll(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Vo(e,t,n=!1){const s=e.children,r=t.children;if(B(s)&&B(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=et(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&Vo(i,l)),l.type===Dn&&(l.el=i.el),l.type===ge&&!l.el&&(l.el=i.el)}}function Nl(e){const t=e.slice(),n=[0];let s,r,o,i,l;const c=e.length;for(s=0;s<c;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<d?o=l+1:i=l;d<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Bo(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Bo(t)}function er(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Fl=Symbol.for("v-scx"),$l=()=>bt(Fl);function ko(e,t,n){return Hl(e,t,n)}function Hl(e,t,n=te){const{immediate:s,deep:r,flush:o,once:i}=n,l=Pe({},n),c=t&&s||!t&&o!=="post";let d;if(tn){if(o==="sync"){const m=$l();d=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=gt,m.resume=gt,m.pause=gt,m}}const f=he;l.call=(m,C,A)=>ke(m,f,C,A);let h=!1;o==="post"?l.scheduler=m=>{Ce(m,f&&f.suspense)}:o!=="sync"&&(h=!0,l.scheduler=(m,C)=>{C?m():Ns(m)}),l.augmentJob=m=>{t&&(m.flags|=4),h&&(m.flags|=2,f&&(m.id=f.uid,m.i=f))};const p=ol(e,t,l);return tn&&(d?d.push(p):c&&p()),p}const Dl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ae(t)}Modifiers`]||e[`${St(t)}Modifiers`];function jl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||te;let r=n;const o=t.startsWith("update:"),i=o&&Dl(s,t.slice(7));i&&(i.trim&&(r=n.map(f=>fe(f)?f.trim():f)),i.number&&(r=n.map(yn)));let l,c=s[l=Kn(t)]||s[l=Kn(Ae(t))];!c&&o&&(c=s[l=Kn(St(t))]),c&&ke(c,e,6,r);const d=s[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,ke(d,e,6,r)}}function Vl(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={};return o?(B(o)?o.forEach(l=>i[l]=null):Pe(i,o),oe(e)&&s.set(e,i),i):(oe(e)&&s.set(e,null),null)}function Hn(e,t){return!e||!An(t)?!1:(t=t.slice(2).replace(/Once$/,""),Y(e,t[0].toLowerCase()+t.slice(1))||Y(e,St(t))||Y(e,t))}function tr(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:d,renderCache:f,props:h,data:p,setupState:m,ctx:C,inheritAttrs:A}=e,j=Sn(e);let F,L;try{if(n.shapeFlag&4){const M=r||s,k=M;F=Ve(d.call(k,M,f,h,m,p,C)),L=l}else{const M=t;F=Ve(M.length>1?M(h,{attrs:l,slots:i,emit:c}):M(h,null)),L=t.props?l:Bl(l)}}catch(M){qt.length=0,Fn(M,e,1),F=be(ge)}let $=F;if(L&&A!==!1){const M=Object.keys(L),{shapeFlag:k}=$;M.length&&k&7&&(o&&M.some(bs)&&(L=kl(L,o)),$=ot($,L,!1,!0))}return n.dirs&&($=ot($,null,!1,!0),$.dirs=$.dirs?$.dirs.concat(n.dirs):n.dirs),n.transition&&Zt($,n.transition),F=$,Sn(j),F}const Bl=e=>{let t;for(const n in e)(n==="class"||n==="style"||An(n))&&((t||(t={}))[n]=e[n]);return t},kl=(e,t)=>{const n={};for(const s in e)(!bs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Kl(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,d=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?nr(s,i,d):!!i;if(c&8){const f=t.dynamicProps;for(let h=0;h<f.length;h++){const p=f[h];if(i[p]!==s[p]&&!Hn(d,p))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?nr(s,i,d):!0:!!i;return!1}function nr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Hn(n,o))return!0}return!1}function Ul({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ko=e=>e.__isSuspense;function Wl(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):cl(e)}const je=Symbol.for("v-fgt"),Dn=Symbol.for("v-txt"),ge=Symbol.for("v-cmt"),pn=Symbol.for("v-stc"),qt=[];let we=null;function ql(e=!1){qt.push(we=e?null:[])}function Gl(){qt.pop(),we=qt[qt.length-1]||null}let en=1;function sr(e,t=!1){en+=e,e<0&&we&&t&&(we.hasOnce=!0)}function Uo(e){return e.dynamicChildren=en>0?we||Pt:null,Gl(),en>0&&we&&we.push(e),e}function Qf(e,t,n,s,r,o){return Uo(qo(e,t,n,s,r,o,!0))}function zl(e,t,n,s,r){return Uo(be(e,t,n,s,r,!0))}function Cn(e){return e?e.__v_isVNode===!0:!1}function dt(e,t){return e.type===t.type&&e.key===t.key}const Wo=({key:e})=>e??null,gn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?fe(e)||le(e)||Z(e)?{i:me,r:e,k:t,f:!!n}:e:null);function qo(e,t=null,n=null,s=0,r=null,o=e===je?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Wo(t),ref:t&&gn(t),scopeId:wo,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:me};return l?(js(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=fe(n)?8:16),en>0&&!i&&we&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&we.push(c),c}const be=Jl;function Jl(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===vl)&&(e=ge),Cn(e)){const l=ot(e,t,!0);return n&&js(l,n),en>0&&!o&&we&&(l.shapeFlag&6?we[we.indexOf(e)]=l:we.push(l)),l.patchFlag=-2,l}if(cc(e)&&(e=e.__vccOpts),t){t=Ql(t);let{class:l,style:c}=t;l&&!fe(l)&&(t.class=Cs(l)),oe(c)&&(Is(c)&&!B(c)&&(c=Pe({},c)),t.style=Es(c))}const i=fe(e)?1:Ko(e)?128:xo(e)?64:oe(e)?4:Z(e)?2:0;return qo(e,t,n,s,r,i,o,!0)}function Ql(e){return e?Is(e)||Fo(e)?Pe({},e):e:null}function ot(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,d=t?Xl(r||{},t):r,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&Wo(d),ref:t&&t.ref?n&&o?B(o)?o.concat(gn(t)):[o,gn(t)]:gn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==je?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ot(e.ssContent),ssFallback:e.ssFallback&&ot(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Zt(f,c.clone(f)),f}function Yl(e=" ",t=0){return be(Dn,null,e,t)}function Yf(e,t){const n=be(pn,null,e);return n.staticCount=t,n}function Xf(e="",t=!1){return t?(ql(),zl(ge,null,e)):be(ge,null,e)}function Ve(e){return e==null||typeof e=="boolean"?be(ge):B(e)?be(je,null,e.slice()):Cn(e)?et(e):be(Dn,null,String(e))}function et(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:ot(e)}function js(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(B(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),js(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Fo(t)?t._ctx=me:r===3&&me&&(me.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Z(t)?(t={default:t,_ctx:me},n=32):(t=String(t),s&64?(n=16,t=[Yl(t)]):n=8);e.children=t,e.shapeFlag|=n}function Xl(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Cs([t.class,s.class]));else if(r==="style")t.style=Es([t.style,s.style]);else if(An(r)){const o=t[r],i=s[r];i&&o!==i&&!(B(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function He(e,t,n,s=null){ke(e,t,7,[n,s])}const Zl=Mo();let ec=0;function tc(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Zl,o={uid:ec++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Xr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Tl(s,r),emitsOptions:Vl(s,r),emit:null,emitted:null,propsDefaults:te,inheritAttrs:s.inheritAttrs,ctx:te,data:te,props:te,attrs:te,slots:te,refs:te,setupState:te,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=jl.bind(null,o),e.ce&&e.ce(o),o}let he=null;const nc=()=>he||me;let wn,hs;{const e=Mn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};wn=t("__VUE_INSTANCE_SETTERS__",n=>he=n),hs=t("__VUE_SSR_SETTERS__",n=>tn=n)}const Vs=e=>{const t=he;return wn(e),e.scope.on(),()=>{e.scope.off(),wn(t)}},rr=()=>{he&&he.scope.off(),wn(null)};function Go(e){return e.vnode.shapeFlag&4}let tn=!1;function sc(e,t=!1,n=!1){t&&hs(t);const{props:s,children:r}=e.vnode,o=Go(e);xl(e,s,o,t),Pl(e,r,n||t);const i=o?rc(e,t):void 0;return t&&hs(!1),i}function rc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Sl);const{setup:s}=n;if(s){st();const r=e.setupContext=s.length>1?ic(e):null,o=Vs(e),i=ln(s,e,0,[e.props,r]),l=Wr(i);if(rt(),o(),(l||e.sp)&&!Ut(e)&&pl(e),l){if(i.then(rr,rr),t)return i.then(c=>{or(e,c)}).catch(c=>{Fn(c,e,0)});e.asyncDep=i}else or(e,i)}else zo(e)}function or(e,t,n){Z(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:oe(t)&&(e.setupState=vo(t)),zo(e)}function zo(e,t,n){const s=e.type;e.render||(e.render=s.render||gt)}const oc={get(e,t){return ae(e,"get",""),e[t]}};function ic(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,oc),slots:e.slots,emit:e.emit,expose:t}}function jn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(vo(Ms(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Wt)return Wt[n](e)},has(t,n){return n in t||n in Wt}})):e.proxy}function lc(e,t=!0){return Z(e)?e.displayName||e.name:e.name||t&&e.__name}function cc(e){return Z(e)&&"__vccOpts"in e}const Oe=(e,t)=>sl(e,t,tn);function Bs(e,t,n){const s=arguments.length;return s===2?oe(t)&&!B(t)?Cn(t)?be(e,null,[t]):be(e,t):be(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Cn(n)&&(n=[n]),be(e,t,n))}const fc="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ds;const ir=typeof window<"u"&&window.trustedTypes;if(ir)try{ds=ir.createPolicy("vue",{createHTML:e=>e})}catch{}const Jo=ds?e=>ds.createHTML(e):e=>e,uc="http://www.w3.org/2000/svg",ac="http://www.w3.org/1998/Math/MathML",qe=typeof document<"u"?document:null,lr=qe&&qe.createElement("template"),hc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?qe.createElementNS(uc,e):t==="mathml"?qe.createElementNS(ac,e):n?qe.createElement(e,{is:n}):qe.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>qe.createTextNode(e),createComment:e=>qe.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>qe.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{lr.innerHTML=Jo(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=lr.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Je="transition",Dt="animation",nn=Symbol("_vtc"),Qo={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},dc=Pe({},Ro,Qo),pc=e=>(e.displayName="Transition",e.props=dc,e),Zf=pc((e,{slots:t})=>Bs(dl,gc(e),t)),ut=(e,t=[])=>{B(e)?e.forEach(n=>n(...t)):e&&e(...t)},cr=e=>e?B(e)?e.some(t=>t.length>1):e.length>1:!1;function gc(e){const t={};for(const I in e)I in Qo||(t[I]=e[I]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:c=o,appearActiveClass:d=i,appearToClass:f=l,leaveFromClass:h=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,C=mc(r),A=C&&C[0],j=C&&C[1],{onBeforeEnter:F,onEnter:L,onEnterCancelled:$,onLeave:M,onLeaveCancelled:k,onBeforeAppear:ne=F,onAppear:G=L,onAppearCancelled:K=$}=t,P=(I,z,ce,ye)=>{I._enterCancelled=ye,at(I,z?f:l),at(I,z?d:i),ce&&ce()},U=(I,z)=>{I._isLeaving=!1,at(I,h),at(I,m),at(I,p),z&&z()},J=I=>(z,ce)=>{const ye=I?G:L,_e=()=>P(z,I,ce);ut(ye,[z,_e]),fr(()=>{at(z,I?c:o),Ue(z,I?f:l),cr(ye)||ur(z,s,A,_e)})};return Pe(t,{onBeforeEnter(I){ut(F,[I]),Ue(I,o),Ue(I,i)},onBeforeAppear(I){ut(ne,[I]),Ue(I,c),Ue(I,d)},onEnter:J(!1),onAppear:J(!0),onLeave(I,z){I._isLeaving=!0;const ce=()=>U(I,z);Ue(I,h),I._enterCancelled?(Ue(I,p),dr()):(dr(),Ue(I,p)),fr(()=>{I._isLeaving&&(at(I,h),Ue(I,m),cr(M)||ur(I,s,j,ce))}),ut(M,[I,ce])},onEnterCancelled(I){P(I,!1,void 0,!0),ut($,[I])},onAppearCancelled(I){P(I,!0,void 0,!0),ut(K,[I])},onLeaveCancelled(I){U(I),ut(k,[I])}})}function mc(e){if(e==null)return null;if(oe(e))return[Xn(e.enter),Xn(e.leave)];{const t=Xn(e);return[t,t]}}function Xn(e){return bi(e)}function Ue(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[nn]||(e[nn]=new Set)).add(t)}function at(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[nn];n&&(n.delete(t),n.size||(e[nn]=void 0))}function fr(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let yc=0;function ur(e,t,n,s){const r=e._endId=++yc,o=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:l,propCount:c}=_c(e,t);if(!i)return s();const d=i+"end";let f=0;const h=()=>{e.removeEventListener(d,p),o()},p=m=>{m.target===e&&++f>=c&&h()};setTimeout(()=>{f<c&&h()},l+1),e.addEventListener(d,p)}function _c(e,t){const n=window.getComputedStyle(e),s=C=>(n[C]||"").split(", "),r=s(`${Je}Delay`),o=s(`${Je}Duration`),i=ar(r,o),l=s(`${Dt}Delay`),c=s(`${Dt}Duration`),d=ar(l,c);let f=null,h=0,p=0;t===Je?i>0&&(f=Je,h=i,p=o.length):t===Dt?d>0&&(f=Dt,h=d,p=c.length):(h=Math.max(i,d),f=h>0?i>d?Je:Dt:null,p=f?f===Je?o.length:c.length:0);const m=f===Je&&/\b(transform|all)(,|$)/.test(s(`${Je}Property`).toString());return{type:f,timeout:h,propCount:p,hasTransform:m}}function ar(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>hr(n)+hr(e[s])))}function hr(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function dr(){return document.body.offsetHeight}function vc(e,t,n){const s=e[nn];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const xn=Symbol("_vod"),Yo=Symbol("_vsh"),eu={beforeMount(e,{value:t},{transition:n}){e[xn]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):jt(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),jt(e,!0),s.enter(e)):s.leave(e,()=>{jt(e,!1)}):jt(e,t))},beforeUnmount(e,{value:t}){jt(e,t)}};function jt(e,t){e.style.display=t?e[xn]:"none",e[Yo]=!t}const bc=Symbol(""),Sc=/(^|;)\s*display\s*:/;function Ec(e,t,n){const s=e.style,r=fe(n);let o=!1;if(n&&!r){if(t)if(fe(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&mn(s,l,"")}else for(const i in t)n[i]==null&&mn(s,i,"");for(const i in n)i==="display"&&(o=!0),mn(s,i,n[i])}else if(r){if(t!==n){const i=s[bc];i&&(n+=";"+i),s.cssText=n,o=Sc.test(n)}}else t&&e.removeAttribute("style");xn in e&&(e[xn]=o?s.display:"",e[Yo]&&(s.display="none"))}const pr=/\s*!important$/;function mn(e,t,n){if(B(n))n.forEach(s=>mn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Cc(e,t);pr.test(n)?e.setProperty(St(s),n.replace(pr,""),"important"):e[s]=n}}const gr=["Webkit","Moz","ms"],Zn={};function Cc(e,t){const n=Zn[t];if(n)return n;let s=Ae(t);if(s!=="filter"&&s in e)return Zn[t]=s;s=In(s);for(let r=0;r<gr.length;r++){const o=gr[r]+s;if(o in e)return Zn[t]=o}return t}const mr="http://www.w3.org/1999/xlink";function yr(e,t,n,s,r,o=Ri(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(mr,t.slice(6,t.length)):e.setAttributeNS(mr,t,n):n==null||o&&!Jr(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Be(n)?String(n):n)}function _r(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Jo(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Jr(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function pt(e,t,n,s){e.addEventListener(t,n,s)}function wc(e,t,n,s){e.removeEventListener(t,n,s)}const vr=Symbol("_vei");function xc(e,t,n,s,r=null){const o=e[vr]||(e[vr]={}),i=o[t];if(s&&i)i.value=s;else{const[l,c]=Rc(t);if(s){const d=o[t]=Pc(s,r);pt(e,l,d,c)}else i&&(wc(e,l,i,c),o[t]=void 0)}}const br=/(?:Once|Passive|Capture)$/;function Rc(e){let t;if(br.test(e)){t={};let s;for(;s=e.match(br);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):St(e.slice(2)),t]}let es=0;const Tc=Promise.resolve(),Ac=()=>es||(Tc.then(()=>es=0),es=Date.now());function Pc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;ke(Oc(s,n.value),t,5,[s])};return n.value=e,n.attached=Ac(),n}function Oc(e,t){if(B(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Sr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Ic=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?vc(e,s,i):t==="style"?Ec(e,n,s):An(t)?bs(t)||xc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Mc(e,t,s,i))?(_r(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&yr(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!fe(s))?_r(e,Ae(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),yr(e,t,s,i))};function Mc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Sr(t)&&Z(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Sr(t)&&fe(n)?!1:t in e}const Rn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return B(t)?n=>dn(t,n):t};function Lc(e){e.target.composing=!0}function Er(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Lt=Symbol("_assign"),tu={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Lt]=Rn(r);const o=s||r.props&&r.props.type==="number";pt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=yn(l)),e[Lt](l)}),n&&pt(e,"change",()=>{e.value=e.value.trim()}),t||(pt(e,"compositionstart",Lc),pt(e,"compositionend",Er),pt(e,"change",Er))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[Lt]=Rn(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?yn(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},nu={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=Pn(t);pt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?yn(Tn(i)):Tn(i));e[Lt](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,$n(()=>{e._assigning=!1})}),e[Lt]=Rn(s)},mounted(e,{value:t}){Cr(e,t)},beforeUpdate(e,t,n){e[Lt]=Rn(n)},updated(e,{value:t}){e._assigning||Cr(e,t)}};function Cr(e,t){const n=e.multiple,s=B(t);if(!(n&&!s&&!Pn(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],l=Tn(i);if(n)if(s){const c=typeof l;c==="string"||c==="number"?i.selected=t.some(d=>String(d)===String(l)):i.selected=Ai(t,l)>-1}else i.selected=t.has(l);else if(Ln(Tn(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Tn(e){return"_value"in e?e._value:e.value}const Nc=["ctrl","shift","alt","meta"],Fc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Nc.some(n=>e[`${n}Key`]&&!t.includes(n))},su=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=Fc[t[i]];if(l&&l(r,t))return}return e(r,...o)})},$c=Pe({patchProp:Ic},hc);let wr;function Hc(){return wr||(wr=Il($c))}const ru=(...e)=>{const t=Hc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=jc(s);if(!r)return;const o=t._component;!Z(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,Dc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Dc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function jc(e){return fe(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Xo;const Vn=e=>Xo=e,Zo=Symbol();function ps(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Gt;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Gt||(Gt={}));function ou(){const e=Zr(!0),t=e.run(()=>Ls({}));let n=[],s=[];const r=Ms({install(o){Vn(r),r._a=o,o.provide(Zo,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const ei=()=>{};function xr(e,t,n,s=ei){e.push(t);const r=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),s())};return!n&&eo()&&Oi(r),r}function Rt(e,...t){e.slice().forEach(n=>{n(...t)})}const Vc=e=>e(),Rr=Symbol(),ts=Symbol();function gs(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],r=e[n];ps(r)&&ps(s)&&e.hasOwnProperty(n)&&!le(s)&&!yt(s)?e[n]=gs(r,s):e[n]=s}return e}const Bc=Symbol();function kc(e){return!ps(e)||!Object.prototype.hasOwnProperty.call(e,Bc)}const{assign:Ye}=Object;function Kc(e){return!!(le(e)&&e.effect)}function Uc(e,t,n,s){const{state:r,actions:o,getters:i}=t,l=n.state.value[e];let c;function d(){l||(n.state.value[e]=r?r():{});const f=Zi(n.state.value[e]);return Ye(f,o,Object.keys(i||{}).reduce((h,p)=>(h[p]=Ms(Oe(()=>{Vn(n);const m=n._s.get(e);return i[p].call(m,m)})),h),{}))}return c=ti(e,d,t,n,s,!0),c}function ti(e,t,n={},s,r,o){let i;const l=Ye({actions:{}},n),c={deep:!0};let d,f,h=[],p=[],m;const C=s.state.value[e];!o&&!C&&(s.state.value[e]={}),Ls({});let A;function j(K){let P;d=f=!1,typeof K=="function"?(K(s.state.value[e]),P={type:Gt.patchFunction,storeId:e,events:m}):(gs(s.state.value[e],K),P={type:Gt.patchObject,payload:K,storeId:e,events:m});const U=A=Symbol();$n().then(()=>{A===U&&(d=!0)}),f=!0,Rt(h,P,s.state.value[e])}const F=o?function(){const{state:P}=n,U=P?P():{};this.$patch(J=>{Ye(J,U)})}:ei;function L(){i.stop(),h=[],p=[],s._s.delete(e)}const $=(K,P="")=>{if(Rr in K)return K[ts]=P,K;const U=function(){Vn(s);const J=Array.from(arguments),I=[],z=[];function ce(ie){I.push(ie)}function ye(ie){z.push(ie)}Rt(p,{args:J,name:U[ts],store:k,after:ce,onError:ye});let _e;try{_e=K.apply(this&&this.$id===e?this:k,J)}catch(ie){throw Rt(z,ie),ie}return _e instanceof Promise?_e.then(ie=>(Rt(I,ie),ie)).catch(ie=>(Rt(z,ie),Promise.reject(ie))):(Rt(I,_e),_e)};return U[Rr]=!0,U[ts]=P,U},M={_p:s,$id:e,$onAction:xr.bind(null,p),$patch:j,$reset:F,$subscribe(K,P={}){const U=xr(h,K,P.detached,()=>J()),J=i.run(()=>ko(()=>s.state.value[e],I=>{(P.flush==="sync"?f:d)&&K({storeId:e,type:Gt.direct,events:m},I)},Ye({},c,P)));return U},$dispose:L},k=Nn(M);s._s.set(e,k);const G=(s._a&&s._a.runWithContext||Vc)(()=>s._e.run(()=>(i=Zr()).run(()=>t({action:$}))));for(const K in G){const P=G[K];if(le(P)&&!Kc(P)||yt(P))o||(C&&kc(P)&&(le(P)?P.value=C[K]:gs(P,C[K])),s.state.value[e][K]=P);else if(typeof P=="function"){const U=$(P,K);G[K]=U,l.actions[K]=P}}return Ye(k,G),Ye(q(k),G),Object.defineProperty(k,"$state",{get:()=>s.state.value[e],set:K=>{j(P=>{Ye(P,K)})}}),s._p.forEach(K=>{Ye(k,i.run(()=>K({store:k,app:s._a,pinia:s,options:l})))}),C&&o&&n.hydrate&&n.hydrate(k.$state,C),d=!0,f=!0,k}/*! #__NO_SIDE_EFFECTS__ */function iu(e,t,n){let s;const r=typeof t=="function";s=r?n:t;function o(i,l){const c=wl();return i=i||(c?bt(Zo,null):null),i&&Vn(i),i=Xo,i._s.has(e)||(r?ti(e,t,s,i):Uc(e,s,i)),i._s.get(e)}return o.$id=e,o}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const At=typeof document<"u";function ni(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Wc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&ni(e.default)}const Q=Object.assign;function ns(e,t){const n={};for(const s in t){const r=t[s];n[s]=Le(r)?r.map(e):e(r)}return n}const zt=()=>{},Le=Array.isArray,si=/#/g,qc=/&/g,Gc=/\//g,zc=/=/g,Jc=/\?/g,ri=/\+/g,Qc=/%5B/g,Yc=/%5D/g,oi=/%5E/g,Xc=/%60/g,ii=/%7B/g,Zc=/%7C/g,li=/%7D/g,ef=/%20/g;function ks(e){return encodeURI(""+e).replace(Zc,"|").replace(Qc,"[").replace(Yc,"]")}function tf(e){return ks(e).replace(ii,"{").replace(li,"}").replace(oi,"^")}function ms(e){return ks(e).replace(ri,"%2B").replace(ef,"+").replace(si,"%23").replace(qc,"%26").replace(Xc,"`").replace(ii,"{").replace(li,"}").replace(oi,"^")}function nf(e){return ms(e).replace(zc,"%3D")}function sf(e){return ks(e).replace(si,"%23").replace(Jc,"%3F")}function rf(e){return e==null?"":sf(e).replace(Gc,"%2F")}function sn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const of=/\/$/,lf=e=>e.replace(of,"");function ss(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=af(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:sn(i)}}function cf(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Tr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function ff(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Nt(t.matched[s],n.matched[r])&&ci(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Nt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ci(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!uf(e[n],t[n]))return!1;return!0}function uf(e,t){return Le(e)?Ar(e,t):Le(t)?Ar(t,e):e===t}function Ar(e,t){return Le(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function af(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const Qe={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var rn;(function(e){e.pop="pop",e.push="push"})(rn||(rn={}));var Jt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Jt||(Jt={}));function hf(e){if(!e)if(At){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),lf(e)}const df=/^[^#]+#/;function pf(e,t){return e.replace(df,"#")+t}function gf(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Bn=()=>({left:window.scrollX,top:window.scrollY});function mf(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=gf(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Pr(e,t){return(history.state?history.state.position-t:-1)+e}const ys=new Map;function yf(e,t){ys.set(e,t)}function _f(e){const t=ys.get(e);return ys.delete(e),t}let vf=()=>location.protocol+"//"+location.host;function fi(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),Tr(c,"")}return Tr(n,e)+s+r}function bf(e,t,n,s){let r=[],o=[],i=null;const l=({state:p})=>{const m=fi(e,location),C=n.value,A=t.value;let j=0;if(p){if(n.value=m,t.value=p,i&&i===C){i=null;return}j=A?p.position-A.position:0}else s(m);r.forEach(F=>{F(n.value,C,{delta:j,type:rn.pop,direction:j?j>0?Jt.forward:Jt.back:Jt.unknown})})};function c(){i=n.value}function d(p){r.push(p);const m=()=>{const C=r.indexOf(p);C>-1&&r.splice(C,1)};return o.push(m),m}function f(){const{history:p}=window;p.state&&p.replaceState(Q({},p.state,{scroll:Bn()}),"")}function h(){for(const p of o)p();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",f)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",f,{passive:!0}),{pauseListeners:c,listen:d,destroy:h}}function Or(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Bn():null}}function Sf(e){const{history:t,location:n}=window,s={value:fi(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,d,f){const h=e.indexOf("#"),p=h>-1?(n.host&&document.querySelector("base")?e:e.slice(h))+c:vf()+e+c;try{t[f?"replaceState":"pushState"](d,"",p),r.value=d}catch{n[f?"replace":"assign"](p)}}function i(c,d){const f=Q({},t.state,Or(r.value.back,c,r.value.forward,!0),d,{position:r.value.position});o(c,f,!0),s.value=c}function l(c,d){const f=Q({},r.value,t.state,{forward:c,scroll:Bn()});o(f.current,f,!0);const h=Q({},Or(s.value,c,null),{position:f.position+1},d);o(c,h,!1),s.value=c}return{location:s,state:r,push:l,replace:i}}function lu(e){e=hf(e);const t=Sf(e),n=bf(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=Q({location:"",base:e,go:s,createHref:pf.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Ef(e){return typeof e=="string"||e&&typeof e=="object"}function ui(e){return typeof e=="string"||typeof e=="symbol"}const ai=Symbol("");var Ir;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ir||(Ir={}));function Ft(e,t){return Q(new Error,{type:e,[ai]:!0},t)}function We(e,t){return e instanceof Error&&ai in e&&(t==null||!!(e.type&t))}const Mr="[^/]+?",Cf={sensitive:!1,strict:!1,start:!0,end:!0},wf=/[.+*?^${}()[\]/\\]/g;function xf(e,t){const n=Q({},Cf,t),s=[];let r=n.start?"^":"";const o=[];for(const d of e){const f=d.length?[]:[90];n.strict&&!d.length&&(r+="/");for(let h=0;h<d.length;h++){const p=d[h];let m=40+(n.sensitive?.25:0);if(p.type===0)h||(r+="/"),r+=p.value.replace(wf,"\\$&"),m+=40;else if(p.type===1){const{value:C,repeatable:A,optional:j,regexp:F}=p;o.push({name:C,repeatable:A,optional:j});const L=F||Mr;if(L!==Mr){m+=10;try{new RegExp(`(${L})`)}catch(M){throw new Error(`Invalid custom RegExp for param "${C}" (${L}): `+M.message)}}let $=A?`((?:${L})(?:/(?:${L}))*)`:`(${L})`;h||($=j&&d.length<2?`(?:/${$})`:"/"+$),j&&($+="?"),r+=$,m+=20,j&&(m+=-8),A&&(m+=-20),L===".*"&&(m+=-50)}f.push(m)}s.push(f)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(d){const f=d.match(i),h={};if(!f)return null;for(let p=1;p<f.length;p++){const m=f[p]||"",C=o[p-1];h[C.name]=m&&C.repeatable?m.split("/"):m}return h}function c(d){let f="",h=!1;for(const p of e){(!h||!f.endsWith("/"))&&(f+="/"),h=!1;for(const m of p)if(m.type===0)f+=m.value;else if(m.type===1){const{value:C,repeatable:A,optional:j}=m,F=C in d?d[C]:"";if(Le(F)&&!A)throw new Error(`Provided param "${C}" is an array but it is not repeatable (* or + modifiers)`);const L=Le(F)?F.join("/"):F;if(!L)if(j)p.length<2&&(f.endsWith("/")?f=f.slice(0,-1):h=!0);else throw new Error(`Missing required param "${C}"`);f+=L}}return f||"/"}return{re:i,score:s,keys:o,parse:l,stringify:c}}function Rf(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function hi(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Rf(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(Lr(s))return 1;if(Lr(r))return-1}return r.length-s.length}function Lr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Tf={type:0,value:""},Af=/[a-zA-Z0-9_]/;function Pf(e){if(!e)return[[]];if(e==="/")return[[Tf]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${d}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,c,d="",f="";function h(){d&&(n===0?o.push({type:0,value:d}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:d,regexp:f,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),d="")}function p(){d+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(d&&h(),i()):c===":"?(h(),n=1):p();break;case 4:p(),n=s;break;case 1:c==="("?n=2:Af.test(c)?p():(h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?f[f.length-1]=="\\"?f=f.slice(0,-1)+c:n=3:f+=c;break;case 3:h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,f="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),h(),i(),r}function Of(e,t,n){const s=xf(Pf(e.path),n),r=Q(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function If(e,t){const n=[],s=new Map;t=Hr({strict:!1,end:!0,sensitive:!1},t);function r(h){return s.get(h)}function o(h,p,m){const C=!m,A=Fr(h);A.aliasOf=m&&m.record;const j=Hr(t,h),F=[A];if("alias"in h){const M=typeof h.alias=="string"?[h.alias]:h.alias;for(const k of M)F.push(Fr(Q({},A,{components:m?m.record.components:A.components,path:k,aliasOf:m?m.record:A})))}let L,$;for(const M of F){const{path:k}=M;if(p&&k[0]!=="/"){const ne=p.record.path,G=ne[ne.length-1]==="/"?"":"/";M.path=p.record.path+(k&&G+k)}if(L=Of(M,p,j),m?m.alias.push(L):($=$||L,$!==L&&$.alias.push(L),C&&h.name&&!$r(L)&&i(h.name)),di(L)&&c(L),A.children){const ne=A.children;for(let G=0;G<ne.length;G++)o(ne[G],L,m&&m.children[G])}m=m||L}return $?()=>{i($)}:zt}function i(h){if(ui(h)){const p=s.get(h);p&&(s.delete(h),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(h);p>-1&&(n.splice(p,1),h.record.name&&s.delete(h.record.name),h.children.forEach(i),h.alias.forEach(i))}}function l(){return n}function c(h){const p=Nf(h,n);n.splice(p,0,h),h.record.name&&!$r(h)&&s.set(h.record.name,h)}function d(h,p){let m,C={},A,j;if("name"in h&&h.name){if(m=s.get(h.name),!m)throw Ft(1,{location:h});j=m.record.name,C=Q(Nr(p.params,m.keys.filter($=>!$.optional).concat(m.parent?m.parent.keys.filter($=>$.optional):[]).map($=>$.name)),h.params&&Nr(h.params,m.keys.map($=>$.name))),A=m.stringify(C)}else if(h.path!=null)A=h.path,m=n.find($=>$.re.test(A)),m&&(C=m.parse(A),j=m.record.name);else{if(m=p.name?s.get(p.name):n.find($=>$.re.test(p.path)),!m)throw Ft(1,{location:h,currentLocation:p});j=m.record.name,C=Q({},p.params,h.params),A=m.stringify(C)}const F=[];let L=m;for(;L;)F.unshift(L.record),L=L.parent;return{name:j,path:A,params:C,matched:F,meta:Lf(F)}}e.forEach(h=>o(h));function f(){n.length=0,s.clear()}return{addRoute:o,resolve:d,removeRoute:i,clearRoutes:f,getRoutes:l,getRecordMatcher:r}}function Nr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Fr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Mf(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Mf(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function $r(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Lf(e){return e.reduce((t,n)=>Q(t,n.meta),{})}function Hr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Nf(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;hi(e,t[o])<0?s=o:n=o+1}const r=Ff(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function Ff(e){let t=e;for(;t=t.parent;)if(di(t)&&hi(e,t)===0)return t}function di({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function $f(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(ri," "),i=o.indexOf("="),l=sn(i<0?o:o.slice(0,i)),c=i<0?null:sn(o.slice(i+1));if(l in t){let d=t[l];Le(d)||(d=t[l]=[d]),d.push(c)}else t[l]=c}return t}function Dr(e){let t="";for(let n in e){const s=e[n];if(n=nf(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Le(s)?s.map(o=>o&&ms(o)):[s&&ms(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Hf(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Le(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const Df=Symbol(""),jr=Symbol(""),Ks=Symbol(""),pi=Symbol(""),_s=Symbol("");function Vt(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function tt(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const d=p=>{p===!1?c(Ft(4,{from:n,to:t})):p instanceof Error?c(p):Ef(p)?c(Ft(2,{from:t,to:p})):(i&&s.enterCallbacks[r]===i&&typeof p=="function"&&i.push(p),l())},f=o(()=>e.call(s&&s.instances[r],t,n,d));let h=Promise.resolve(f);e.length<3&&(h=h.then(d)),h.catch(p=>c(p))})}function rs(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(ni(c)){const f=(c.__vccOpts||c)[t];f&&o.push(tt(f,n,s,i,l,r))}else{let d=c();o.push(()=>d.then(f=>{if(!f)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const h=Wc(f)?f.default:f;i.mods[l]=f,i.components[l]=h;const m=(h.__vccOpts||h)[t];return m&&tt(m,n,s,i,l,r)()}))}}return o}function Vr(e){const t=bt(Ks),n=bt(pi),s=Oe(()=>{const c=It(e.to);return t.resolve(c)}),r=Oe(()=>{const{matched:c}=s.value,{length:d}=c,f=c[d-1],h=n.matched;if(!f||!h.length)return-1;const p=h.findIndex(Nt.bind(null,f));if(p>-1)return p;const m=Br(c[d-2]);return d>1&&Br(f)===m&&h[h.length-1].path!==m?h.findIndex(Nt.bind(null,c[d-2])):p}),o=Oe(()=>r.value>-1&&Kf(n.params,s.value.params)),i=Oe(()=>r.value>-1&&r.value===n.matched.length-1&&ci(n.params,s.value.params));function l(c={}){if(kf(c)){const d=t[It(e.replace)?"replace":"push"](It(e.to)).catch(zt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:s,href:Oe(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function jf(e){return e.length===1?e[0]:e}const Vf=Io({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Vr,setup(e,{slots:t}){const n=Nn(Vr(e)),{options:s}=bt(Ks),r=Oe(()=>({[kr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[kr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&jf(t.default(n));return e.custom?o:Bs("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),Bf=Vf;function kf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Kf(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Le(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function Br(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const kr=(e,t,n)=>e??t??n,Uf=Io({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=bt(_s),r=Oe(()=>e.route||s.value),o=bt(jr,0),i=Oe(()=>{let d=It(o);const{matched:f}=r.value;let h;for(;(h=f[d])&&!h.components;)d++;return d}),l=Oe(()=>r.value.matched[i.value]);Qn(jr,Oe(()=>i.value+1)),Qn(Df,l),Qn(_s,r);const c=Ls();return ko(()=>[c.value,l.value,e.name],([d,f,h],[p,m,C])=>{f&&(f.instances[h]=d,m&&m!==f&&d&&d===p&&(f.leaveGuards.size||(f.leaveGuards=m.leaveGuards),f.updateGuards.size||(f.updateGuards=m.updateGuards))),d&&f&&(!m||!Nt(f,m)||!p)&&(f.enterCallbacks[h]||[]).forEach(A=>A(d))},{flush:"post"}),()=>{const d=r.value,f=e.name,h=l.value,p=h&&h.components[f];if(!p)return Kr(n.default,{Component:p,route:d});const m=h.props[f],C=m?m===!0?d.params:typeof m=="function"?m(d):m:null,j=Bs(p,Q({},C,t,{onVnodeUnmounted:F=>{F.component.isUnmounted&&(h.instances[f]=null)},ref:c}));return Kr(n.default,{Component:j,route:d})||j}}});function Kr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Wf=Uf;function cu(e){const t=If(e.routes,e),n=e.parseQuery||$f,s=e.stringifyQuery||Dr,r=e.history,o=Vt(),i=Vt(),l=Vt(),c=Qi(Qe);let d=Qe;At&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const f=ns.bind(null,_=>""+_),h=ns.bind(null,rf),p=ns.bind(null,sn);function m(_,O){let R,N;return ui(_)?(R=t.getRecordMatcher(_),N=O):N=_,t.addRoute(N,R)}function C(_){const O=t.getRecordMatcher(_);O&&t.removeRoute(O)}function A(){return t.getRoutes().map(_=>_.record)}function j(_){return!!t.getRecordMatcher(_)}function F(_,O){if(O=Q({},O||c.value),typeof _=="string"){const g=ss(n,_,O.path),y=t.resolve({path:g.path},O),b=r.createHref(g.fullPath);return Q(g,y,{params:p(y.params),hash:sn(g.hash),redirectedFrom:void 0,href:b})}let R;if(_.path!=null)R=Q({},_,{path:ss(n,_.path,O.path).path});else{const g=Q({},_.params);for(const y in g)g[y]==null&&delete g[y];R=Q({},_,{params:h(g)}),O.params=h(O.params)}const N=t.resolve(R,O),ee=_.hash||"";N.params=f(p(N.params));const u=cf(s,Q({},_,{hash:tf(ee),path:N.path})),a=r.createHref(u);return Q({fullPath:u,hash:ee,query:s===Dr?Hf(_.query):_.query||{}},N,{redirectedFrom:void 0,href:a})}function L(_){return typeof _=="string"?ss(n,_,c.value.path):Q({},_)}function $(_,O){if(d!==_)return Ft(8,{from:O,to:_})}function M(_){return G(_)}function k(_){return M(Q(L(_),{replace:!0}))}function ne(_){const O=_.matched[_.matched.length-1];if(O&&O.redirect){const{redirect:R}=O;let N=typeof R=="function"?R(_):R;return typeof N=="string"&&(N=N.includes("?")||N.includes("#")?N=L(N):{path:N},N.params={}),Q({query:_.query,hash:_.hash,params:N.path!=null?{}:_.params},N)}}function G(_,O){const R=d=F(_),N=c.value,ee=_.state,u=_.force,a=_.replace===!0,g=ne(R);if(g)return G(Q(L(g),{state:typeof g=="object"?Q({},ee,g.state):ee,force:u,replace:a}),O||R);const y=R;y.redirectedFrom=O;let b;return!u&&ff(s,N,R)&&(b=Ft(16,{to:y,from:N}),lt(N,N,!0,!1)),(b?Promise.resolve(b):U(y,N)).catch(v=>We(v)?We(v,2)?v:Et(v):it(v,y,N)).then(v=>{if(v){if(We(v,2))return G(Q({replace:a},L(v.to),{state:typeof v.to=="object"?Q({},ee,v.to.state):ee,force:u}),O||y)}else v=I(y,N,!0,a,ee);return J(y,N,v),v})}function K(_,O){const R=$(_,O);return R?Promise.reject(R):Promise.resolve()}function P(_){const O=wt.values().next().value;return O&&typeof O.runWithContext=="function"?O.runWithContext(_):_()}function U(_,O){let R;const[N,ee,u]=qf(_,O);R=rs(N.reverse(),"beforeRouteLeave",_,O);for(const g of N)g.leaveGuards.forEach(y=>{R.push(tt(y,_,O))});const a=K.bind(null,_,O);return R.push(a),Re(R).then(()=>{R=[];for(const g of o.list())R.push(tt(g,_,O));return R.push(a),Re(R)}).then(()=>{R=rs(ee,"beforeRouteUpdate",_,O);for(const g of ee)g.updateGuards.forEach(y=>{R.push(tt(y,_,O))});return R.push(a),Re(R)}).then(()=>{R=[];for(const g of u)if(g.beforeEnter)if(Le(g.beforeEnter))for(const y of g.beforeEnter)R.push(tt(y,_,O));else R.push(tt(g.beforeEnter,_,O));return R.push(a),Re(R)}).then(()=>(_.matched.forEach(g=>g.enterCallbacks={}),R=rs(u,"beforeRouteEnter",_,O,P),R.push(a),Re(R))).then(()=>{R=[];for(const g of i.list())R.push(tt(g,_,O));return R.push(a),Re(R)}).catch(g=>We(g,8)?g:Promise.reject(g))}function J(_,O,R){l.list().forEach(N=>P(()=>N(_,O,R)))}function I(_,O,R,N,ee){const u=$(_,O);if(u)return u;const a=O===Qe,g=At?history.state:{};R&&(N||a?r.replace(_.fullPath,Q({scroll:a&&g&&g.scroll},ee)):r.push(_.fullPath,ee)),c.value=_,lt(_,O,R,a),Et()}let z;function ce(){z||(z=r.listen((_,O,R)=>{if(!cn.listening)return;const N=F(_),ee=ne(N);if(ee){G(Q(ee,{replace:!0,force:!0}),N).catch(zt);return}d=N;const u=c.value;At&&yf(Pr(u.fullPath,R.delta),Bn()),U(N,u).catch(a=>We(a,12)?a:We(a,2)?(G(Q(L(a.to),{force:!0}),N).then(g=>{We(g,20)&&!R.delta&&R.type===rn.pop&&r.go(-1,!1)}).catch(zt),Promise.reject()):(R.delta&&r.go(-R.delta,!1),it(a,N,u))).then(a=>{a=a||I(N,u,!1),a&&(R.delta&&!We(a,8)?r.go(-R.delta,!1):R.type===rn.pop&&We(a,20)&&r.go(-1,!1)),J(N,u,a)}).catch(zt)}))}let ye=Vt(),_e=Vt(),ie;function it(_,O,R){Et(_);const N=_e.list();return N.length&&N.forEach(ee=>ee(_,O,R)),Promise.reject(_)}function kn(){return ie&&c.value!==Qe?Promise.resolve():new Promise((_,O)=>{ye.add([_,O])})}function Et(_){return ie||(ie=!_,ce(),ye.list().forEach(([O,R])=>_?R(_):O()),ye.reset()),_}function lt(_,O,R,N){const{scrollBehavior:ee}=e;if(!At||!ee)return Promise.resolve();const u=!R&&_f(Pr(_.fullPath,0))||(N||!R)&&history.state&&history.state.scroll||null;return $n().then(()=>ee(_,O,u)).then(a=>a&&mf(a)).catch(a=>it(a,_,O))}const xe=_=>r.go(_);let Ct;const wt=new Set,cn={currentRoute:c,listening:!0,addRoute:m,removeRoute:C,clearRoutes:t.clearRoutes,hasRoute:j,getRoutes:A,resolve:F,options:e,push:M,replace:k,go:xe,back:()=>xe(-1),forward:()=>xe(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:_e.add,isReady:kn,install(_){const O=this;_.component("RouterLink",Bf),_.component("RouterView",Wf),_.config.globalProperties.$router=O,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>It(c)}),At&&!Ct&&c.value===Qe&&(Ct=!0,M(r.location).catch(ee=>{}));const R={};for(const ee in Qe)Object.defineProperty(R,ee,{get:()=>c.value[ee],enumerable:!0});_.provide(Ks,O),_.provide(pi,mo(R)),_.provide(_s,c);const N=_.unmount;wt.add(_),_.unmount=function(){wt.delete(_),wt.size<1&&(d=Qe,z&&z(),z=null,c.value=Qe,Ct=!1,ie=!1),N()}}};function Re(_){return _.reduce((O,R)=>O.then(()=>P(R)),Promise.resolve())}return cn}function qf(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(d=>Nt(d,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(d=>Nt(d,c))||r.push(c))}return[n,s,r]}export{cu as A,lu as B,Nn as C,ru as D,ou as E,fc as F,Zf as T,be as a,ql as b,Qf as c,Io as d,Ls as e,zf as f,iu as g,Oe as h,qo as i,Yl as j,tu as k,su as l,nu as m,Es as n,ml as o,Yf as p,Xf as q,Jf as r,Cs as s,Pi as t,It as u,eu as v,Gf as w,fl as x,ko as y,zl as z};
