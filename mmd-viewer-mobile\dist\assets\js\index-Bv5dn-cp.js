const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/AboutView-d002Q_8Y.js","assets/js/vue-vendor-BG9NRnXl.js","assets/js/three-CJ0eQX7f.js","assets/js/utils-DhSJXSu-.js","assets/css/AboutView-BfXZmPum.css"])))=>i.map(i=>d[i]);
import{d as Q,o as W,c as I,a as Y,r as ye,b as _,e as d,f as J,g as Le,h as ee,i as e,t as Z,w as q,v as ce,j as se,k as te,l as he,n as le,m as ie,p as oe,q as N,s as ne,u as j,T as me,x as pe,y as Me,z as Ce,A as ke,B as Ae,C as Ve,D as Se,E as Ee}from"./vue-vendor-BG9NRnXl.js";import{B as He,M as xe,a as $e,c as Pe,d as be,P as re,W as De,e as Te,f as _e,g as Ie,D as fe,h as G,i as ue,Q as Fe}from"./three-CJ0eQX7f.js";import{M as Re,a as ze}from"./utils-DhSJXSu-.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))i(r);new MutationObserver(r=>{for(const a of r)if(a.type==="childList")for(const m of a.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&i(m)}).observe(document,{childList:!0,subtree:!0});function n(r){const a={};return r.integrity&&(a.integrity=r.integrity),r.referrerPolicy&&(a.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?a.credentials="include":r.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function i(r){if(r.ep)return;r.ep=!0;const a=n(r);fetch(r.href,a)}})();const Be={id:"app",class:"app-container"},Ue=Q({__name:"App",setup(o){return W(()=>{document.addEventListener("touchstart",n=>{n.touches.length>1&&n.preventDefault()},{passive:!1});let t=0;document.addEventListener("touchend",n=>{const i=Date.now();i-t<=300&&n.preventDefault(),t=i},!1)}),(t,n)=>{const i=ye("router-view");return _(),I("div",Be,[Y(i)])}}}),Oe="modulepreload",Ze=function(o){return"/"+o},ge={},Xe=function(t,n,i){let r=Promise.resolve();if(n&&n.length>0){let m=function(b){return Promise.all(b.map(C=>Promise.resolve(C).then(E=>({status:"fulfilled",value:E}),E=>({status:"rejected",reason:E}))))};document.getElementsByTagName("link");const v=document.querySelector("meta[property=csp-nonce]"),p=(v==null?void 0:v.nonce)||(v==null?void 0:v.getAttribute("nonce"));r=m(n.map(b=>{if(b=Ze(b),b in ge)return;ge[b]=!0;const C=b.endsWith(".css"),E=C?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${b}"]${E}`))return;const f=document.createElement("link");if(f.rel=C?"stylesheet":Oe,C||(f.as="script"),f.crossOrigin="",f.href=b,p&&f.setAttribute("nonce",p),document.head.appendChild(f),C)return new Promise(($,A)=>{f.addEventListener("load",$),f.addEventListener("error",()=>A(new Error(`Unable to preload CSS for ${b}`)))})}))}function a(m){const v=new Event("vite:preloadError",{cancelable:!0});if(v.payload=m,window.dispatchEvent(v),!v.defaultPrevented)throw m}return r.then(m=>{for(const v of m||[])v.status==="rejected"&&a(v.reason);return t().catch(a)})};function qe(){const o=d(),t=d(),n=d(),i=d(),r=d(),a=d(),m=d(),v=d(),p=async s=>{await new Promise(k=>{if(window.Ammo)k();else{const h=()=>{window.Ammo?k():setTimeout(h,100)};h()}}),o.value=new Pe,o.value.background=new be(0);const y=s.clientWidth/s.clientHeight;t.value=new re(45,y,.1,1e3),t.value.position.set(0,10,30),t.value.lookAt(0,10,0),n.value=new De({antialias:!0,alpha:!0,powerPreference:"high-performance"}),n.value.setSize(s.clientWidth,s.clientHeight),n.value.setPixelRatio(Math.min(window.devicePixelRatio,2)),n.value.shadowMap.enabled=!0,n.value.shadowMap.type=Te,n.value.outputColorSpace=_e,s.appendChild(n.value.domElement),b(),i.value=new Re,r.value=new ze,C(s),E();const S=()=>{if(t.value&&n.value){const k=s.clientWidth/s.clientHeight;t.value.aspect=k,t.value.updateProjectionMatrix(),n.value.setSize(s.clientWidth,s.clientHeight)}};return window.addEventListener("resize",S),()=>{window.removeEventListener("resize",S)}},b=()=>{if(!o.value)return;const s=new Ie(4210752,.4);o.value.add(s);const y=new fe(16777215,.8);y.position.set(10,10,5),y.castShadow=!0,y.shadow.mapSize.width=2048,y.shadow.mapSize.height=2048,o.value.add(y);const S=new fe(16777215,.3);S.position.set(-10,5,-5),o.value.add(S)},C=s=>{let y=!1,S=0,k=0;const h=x=>{y=!0,S=x.clientX,k=x.clientY},u=x=>{if(!y||!t.value)return;const g=x.clientX-S,M=x.clientY-k,l=new ue,L=new G;L.copy(t.value.position).sub(new G(0,10,0)),l.setFromVector3(L),l.theta-=g*.01,l.phi+=M*.01,l.phi=Math.max(.1,Math.min(Math.PI-.1,l.phi)),L.setFromSpherical(l),t.value.position.copy(new G(0,10,0)).add(L),t.value.lookAt(0,10,0),S=x.clientX,k=x.clientY},c=()=>{y=!1},H=x=>{if(!t.value)return;const g=x.deltaY>0?1.1:.9,M=new G;t.value.getWorldDirection(M),t.value.position.add(M.multiplyScalar((g-1)*5))};return s.addEventListener("mousedown",h),s.addEventListener("mousemove",u),s.addEventListener("mouseup",c),s.addEventListener("wheel",H),()=>{s.removeEventListener("mousedown",h),s.removeEventListener("mousemove",u),s.removeEventListener("mouseup",c),s.removeEventListener("wheel",H)}},E=()=>{const s=()=>{requestAnimationFrame(s),r.value&&r.value.update(),n.value&&o.value&&t.value&&n.value.render(o.value,t.value)};s()};return{scene:o,camera:t,renderer:n,currentModel:a,initScene:p,loadModel:async s=>{if(!i.value||!o.value)throw new Error("场景未初始化");return new Promise((y,S)=>{const k=new FileReader;k.onload=h=>{var u;(u=h.target)==null||u.result;try{const c=new He(2,4,1),H=new xe({color:65280}),x=new $e(c,H);x.position.y=2,a.value&&o.value.remove(a.value),a.value=x,o.value.add(x),y(x)}catch(c){S(c)}},k.onerror=()=>S(new Error("文件读取失败")),k.readAsArrayBuffer(s)})},loadMotion:async s=>new Promise((y,S)=>{const k=new FileReader;k.onload=h=>{try{const u={duration:10};m.value=u,y(u)}catch(u){S(u)}},k.onerror=()=>S(new Error("文件读取失败")),k.readAsArrayBuffer(s)}),loadMusic:async s=>new Promise((y,S)=>{const k=new Audio,h=URL.createObjectURL(s);k.onloadeddata=()=>{v.value=k,y(k)},k.onerror=()=>{URL.revokeObjectURL(h),S(new Error("音频加载失败"))},k.src=h}),cleanup:()=>{n.value&&n.value.dispose(),v.value&&(v.value.pause(),v.value=void 0)}}}function Ge(){const o=d(!1),t=d(0),n=d(0),i=d(),r=d(0),a=()=>{i.value&&i.value.play(),o.value||(r.value=Date.now()-t.value*1e3,o.value=!0)},m=()=>{i.value&&i.value.pause(),o.value=!1},v=()=>{i.value&&(i.value.pause(),i.value.currentTime=0),o.value=!1,t.value=0,r.value=0};return{isPlaying:o,currentTime:t,duration:n,play:a,pause:m,stop:v,seek:A=>{t.value=A,i.value&&(i.value.currentTime=A),o.value&&(r.value=Date.now()-A*1e3)},setAudio:A=>{i.value=A,n.value=A.duration||0,A.addEventListener("loadedmetadata",()=>{n.value=A.duration}),A.addEventListener("timeupdate",()=>{o.value&&(t.value=A.currentTime)}),A.addEventListener("ended",()=>{v()})},getCurrentTime:()=>o.value?(Date.now()-r.value)/1e3:t.value,getDuration:()=>n.value,setPlaybackRate:A=>{i.value&&(i.value.playbackRate=A)},setVolume:A=>{i.value&&(i.value.volume=Math.max(0,Math.min(1,A)))}}}function je(o,t,n=new G){const i=d(!0),r=d(!1),a=d(!1),m=d([]),v=d(0),p=d({x:0,y:0}),b=d(new ue),C=d(new ue),E=d(new G);d(!1);const f={enableRotate:!0,enableZoom:!0,enablePan:!0,rotateSpeed:1,zoomSpeed:2,panSpeed:1,minDistance:1,maxDistance:100,minPolarAngle:0,maxPolarAngle:Math.PI,minAzimuthAngle:-1/0,maxAzimuthAngle:1/0,dampingFactor:.05,enableDamping:!0},$=(l,L)=>{const T=l.clientX-L.clientX,R=l.clientY-L.clientY;return Math.sqrt(T*T+R*R)},A=(l,L)=>({x:(l.clientX+L.clientX)/2,y:(l.clientY+L.clientY)/2}),P=l=>{C.value.theta-=l},s=l=>{C.value.phi-=l},y=(l,L)=>{const T=new G;T.setFromMatrixColumn(L,0),T.multiplyScalar(-l),E.value.add(T)},S=(l,L)=>{const T=new G;T.setFromMatrixColumn(L,1),T.multiplyScalar(l),E.value.add(T)},k=(l,L)=>{const T=t.domElement,R=new G;R.copy(o.position).sub(n);let w=R.length();w*=Math.tan(o.fov/2*Math.PI/180),y(2*l*w/T.clientHeight,o.matrix),S(2*L*w/T.clientHeight,o.matrix)},h=l=>{o instanceof re&&(C.value.radius*=l)},u=l=>{o instanceof re&&(C.value.radius/=l)},c=l=>{i.value&&(l.preventDefault(),m.value=Array.from(l.touches),m.value.length===1?f.enableRotate&&(r.value=!0):m.value.length===2&&f.enableZoom&&(a.value=!0,v.value=$(m.value[0],m.value[1]),p.value=A(m.value[0],m.value[1])))},H=l=>{if(!i.value)return;l.preventDefault();const L=Array.from(l.touches);if(L.length===1&&r.value){if(f.enableRotate){const R=t.domElement.getBoundingClientRect(),w=L[0].clientX-m.value[0].clientX,B=L[0].clientY-m.value[0].clientY;P(2*Math.PI*w/R.height*f.rotateSpeed),s(2*Math.PI*B/R.height*f.rotateSpeed),m.value=L}}else if(L.length===2&&a.value){const T=$(L[0],L[1]),R=A(L[0],L[1]);if(f.enableZoom){const w=T/v.value;w>1?u(Math.pow(.95,f.zoomSpeed)):w<1&&h(Math.pow(.95,f.zoomSpeed)),v.value=T}if(f.enablePan){const w=R.x-p.value.x,B=R.y-p.value.y;k(w,B),p.value=R}}},x=l=>{i.value&&(l.preventDefault(),l.touches.length===0?(r.value=!1,a.value=!1,m.value=[]):(m.value=Array.from(l.touches),m.value.length===1?a.value=!1:m.value.length===2&&(r.value=!1,v.value=$(m.value[0],m.value[1]),p.value=A(m.value[0],m.value[1]))))},g=()=>{const l=new G,L=new Fe().setFromUnitVectors(o.up,new G(0,1,0)),T=L.clone().invert();l.copy(o.position).sub(n),l.applyQuaternion(L),b.value.setFromVector3(l),f.enableDamping?(b.value.theta+=C.value.theta*f.dampingFactor,b.value.phi+=C.value.phi*f.dampingFactor,b.value.radius+=C.value.radius*f.dampingFactor):(b.value.theta+=C.value.theta,b.value.phi+=C.value.phi,b.value.radius+=C.value.radius),b.value.theta=Math.max(f.minAzimuthAngle,Math.min(f.maxAzimuthAngle,b.value.theta)),b.value.phi=Math.max(f.minPolarAngle,Math.min(f.maxPolarAngle,b.value.phi)),b.value.radius=Math.max(f.minDistance,Math.min(f.maxDistance,b.value.radius)),l.setFromSpherical(b.value),l.applyQuaternion(T),o.position.copy(n).add(l),o.lookAt(n),f.enableDamping?(C.value.theta*=1-f.dampingFactor,C.value.phi*=1-f.dampingFactor,C.value.radius*=1-f.dampingFactor):C.value.set(0,0,0),n.add(E.value),f.enableDamping?E.value.multiplyScalar(1-f.dampingFactor):E.value.set(0,0,0)},M=()=>{n.set(0,0,0),o.position.set(0,10,30),o.lookAt(n),b.value.setFromVector3(o.position.clone().sub(n)),C.value.set(0,0,0),E.value.set(0,0,0)};return W(()=>{const l=t.domElement;l.addEventListener("touchstart",c,{passive:!1}),l.addEventListener("touchmove",H,{passive:!1}),l.addEventListener("touchend",x,{passive:!1}),b.value.setFromVector3(o.position.clone().sub(n))}),J(()=>{const l=t.domElement;l.removeEventListener("touchstart",c),l.removeEventListener("touchmove",H),l.removeEventListener("touchend",x)}),{isEnabled:i,isDragging:r,isPinching:a,settings:f,update:g,reset:M}}const We=Le("mmd",()=>{const o=d([]),t=d([]),n=d([]),i=d(null),r=d(null),a=d(null),m=d(!1),v=d(0),p=d(1),b=d(1),C=d(!1),E=d({backgroundColor:"#000000",ambientLightIntensity:.4,directionalLightIntensity:.8,cameraFov:45,showGrid:!1,wireframeMode:!1}),f=d(!1),$=d(!1),A=d(""),P=ee(()=>a.value?a.value.duration:r.value?r.value.duration:0),s=ee(()=>P.value===0?0:v.value/P.value),y=ee(()=>i.value!==null),S=ee(()=>r.value!==null),k=ee(()=>a.value!==null),h=V=>{o.value.push(V),i.value||(i.value=V)},u=V=>{var O;const z=o.value.findIndex(X=>X.id===V);if(z!==-1){const X=o.value[z];((O=i.value)==null?void 0:O.id)===V&&(i.value=null),o.value.splice(z,1),X.object.parent&&X.object.parent.remove(X.object)}},c=V=>{const z=o.value.find(O=>O.id===V);z&&(i.value=z)},H=V=>{t.value.push(V),r.value||(r.value=V)},x=V=>{var O;const z=t.value.findIndex(X=>X.id===V);z!==-1&&(((O=r.value)==null?void 0:O.id)===V&&(r.value=null),t.value.splice(z,1))},g=V=>{const z=t.value.find(O=>O.id===V);z&&(r.value=z)},M=V=>{n.value.push(V),a.value||(a.value=V)},l=V=>{var O;const z=n.value.findIndex(X=>X.id===V);if(z!==-1){const X=n.value[z];((O=a.value)==null?void 0:O.id)===V&&(a.value=null),n.value.splice(z,1),X.element.pause(),X.element.src=""}},L=V=>{const z=n.value.find(O=>O.id===V);z&&(a.value=z)},T=()=>{m.value=!0,a.value&&a.value.element.play()},R=()=>{m.value=!1,a.value&&a.value.element.pause()},w=()=>{m.value=!1,v.value=0,a.value&&(a.value.element.pause(),a.value.element.currentTime=0)},B=V=>{v.value=V,a.value&&(a.value.element.currentTime=V)},F=V=>{p.value=V,a.value&&(a.value.element.playbackRate=V)},D=V=>{b.value=V,C.value=V===0,a.value&&(a.value.element.volume=V)},U=()=>{C.value=!C.value,a.value&&(a.value.element.muted=C.value)},we=V=>{Object.assign(E.value,V)},ve=()=>{E.value={backgroundColor:"#000000",ambientLightIntensity:.4,directionalLightIntensity:.8,cameraFov:45,showGrid:!1,wireframeMode:!1}};return{models:o,motions:t,audios:n,currentModel:i,currentMotion:r,currentAudio:a,isPlaying:m,currentTime:v,playbackRate:p,volume:b,isMuted:C,sceneSettings:E,sidebarVisible:f,loading:$,loadingMessage:A,totalDuration:P,progress:s,hasModel:y,hasMotion:S,hasAudio:k,addModel:h,removeModel:u,setCurrentModel:c,addMotion:H,removeMotion:x,setCurrentMotion:g,addAudio:M,removeAudio:l,setCurrentAudio:L,play:T,pause:R,stop:w,seek:B,setPlaybackRate:F,setVolume:D,toggleMute:U,updateSceneSettings:we,resetSceneSettings:ve,toggleSidebar:()=>{f.value=!f.value},showLoading:(V="加载中...")=>{$.value=!0,A.value=V},hideLoading:()=>{$.value=!1,A.value=""},clearAll:()=>{w(),o.value.forEach(V=>{V.object.parent&&V.object.parent.remove(V.object)}),o.value=[],i.value=null,t.value=[],r.value=null,n.value.forEach(V=>{V.element.pause(),V.element.src=""}),n.value=[],a.value=null,ve()}}}),Ye={class:"top-toolbar"},Ne={class:"toolbar-left"},Qe={class:"toolbar-right"},Je={class:"icon",viewBox:"0 0 24 24"},Ke={key:0,d:"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"},et={key:1,d:"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"},tt={class:"mobile-hidden"},nt=Q({__name:"TopToolbar",emits:["toggle-sidebar","show-help"],setup(o){const t=d(!1),n=()=>{document.fullscreenElement?document.exitFullscreen():document.documentElement.requestFullscreen()},i=()=>{t.value=!!document.fullscreenElement};return W(()=>{document.addEventListener("fullscreenchange",i)}),J(()=>{document.removeEventListener("fullscreenchange",i)}),(r,a)=>(_(),I("div",Ye,[e("div",Ne,[e("button",{class:"btn toolbar-btn",onClick:a[0]||(a[0]=m=>r.$emit("toggle-sidebar"))},a[2]||(a[2]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"})],-1),e("span",{class:"desktop-hidden"},"菜单",-1)]))]),a[4]||(a[4]=e("div",{class:"toolbar-center"},[e("h1",{class:"title"},"MMD查看器")],-1)),e("div",Qe,[e("button",{class:"btn toolbar-btn",onClick:a[1]||(a[1]=m=>r.$emit("show-help"))},a[3]||(a[3]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"})],-1),e("span",{class:"mobile-hidden"},"帮助",-1)])),e("button",{class:"btn toolbar-btn",onClick:n},[(_(),I("svg",Je,[t.value?(_(),I("path",et)):(_(),I("path",Ke))])),e("span",tt,Z(t.value?"退出全屏":"全屏"),1)])])]))}}),K=(o,t)=>{const n=o.__vccOpts||o;for(const[i,r]of t)n[i]=r;return n},at=K(nt,[["__scopeId","data-v-57e72353"]]),ot={class:"sidebar-header"},st={class:"sidebar-content"},lt={class:"section"},it={class:"file-group"},rt={class:"file-label"},ut={class:"file-group"},ct={class:"file-label"},dt={class:"file-group"},vt={class:"file-label"},mt={class:"section"},pt={class:"control-group"},ft={class:"control-group"},gt={class:"value"},ht={class:"control-group"},bt={class:"value"},wt={class:"section"},yt={class:"control-group"},Lt={class:"value"},Mt=Q({__name:"Sidebar",props:{visible:{type:Boolean}},emits:["update:visible","load-model","load-motion","load-music"],setup(o,{emit:t}){const n=t,i=d("#000000"),r=d(.4),a=d(.8),m=d(45),v=P=>{var S;const s=P.target,y=(S=s.files)==null?void 0:S[0];y&&(n("load-model",y),s.value="")},p=P=>{var S;const s=P.target,y=(S=s.files)==null?void 0:S[0];y&&(n("load-motion",y),s.value="")},b=P=>{var S;const s=P.target,y=(S=s.files)==null?void 0:S[0];y&&(n("load-music",y),s.value="")},C=()=>{},E=()=>{},f=()=>{},$=()=>{},A=()=>{};return(P,s)=>q((_(),I("div",{class:"sidebar-overlay",onClick:s[6]||(s[6]=y=>P.$emit("update:visible",!1))},[e("div",{class:"sidebar",onClick:s[5]||(s[5]=he(()=>{},["stop"]))},[e("div",ot,[s[8]||(s[8]=e("h2",null,"控制面板",-1)),e("button",{class:"btn close-btn",onClick:s[0]||(s[0]=y=>P.$emit("update:visible",!1))},s[7]||(s[7]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})],-1)]))]),e("div",st,[e("div",lt,[s[12]||(s[12]=e("h3",null,"文件加载",-1)),e("div",it,[e("label",rt,[e("input",{type:"file",accept:".pmd,.pmx",onChange:v,class:"file-input"},null,32),s[9]||(s[9]=e("span",{class:"file-button"},[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"})]),se(" 加载模型 (.pmd/.pmx) ")],-1))])]),e("div",ut,[e("label",ct,[e("input",{type:"file",accept:".vmd",onChange:p,class:"file-input"},null,32),s[10]||(s[10]=e("span",{class:"file-button"},[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M10,8L16,12L10,16V8Z"})]),se(" 加载动作 (.vmd) ")],-1))])]),e("div",dt,[e("label",vt,[e("input",{type:"file",accept:".mp3,.wav,.ogg",onChange:b,class:"file-input"},null,32),s[11]||(s[11]=e("span",{class:"file-button"},[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M12,3V13.55C11.41,13.21 10.73,13 10,13A3,3 0 0,0 7,16A3,3 0 0,0 10,19A3,3 0 0,0 13,16V7H17V5H12V3Z"})]),se(" 加载音乐 ")],-1))])])]),e("div",mt,[s[16]||(s[16]=e("h3",null,"场景设置",-1)),e("div",pt,[s[13]||(s[13]=e("label",null,"背景颜色",-1)),q(e("input",{type:"color","onUpdate:modelValue":s[1]||(s[1]=y=>i.value=y),onChange:C,class:"color-input"},null,544),[[te,i.value]])]),e("div",ft,[s[14]||(s[14]=e("label",null,"环境光强度",-1)),q(e("input",{type:"range",min:"0",max:"1",step:"0.1","onUpdate:modelValue":s[2]||(s[2]=y=>r.value=y),onInput:E,class:"range-input"},null,544),[[te,r.value]]),e("span",gt,Z(r.value),1)]),e("div",ht,[s[15]||(s[15]=e("label",null,"主光源强度",-1)),q(e("input",{type:"range",min:"0",max:"2",step:"0.1","onUpdate:modelValue":s[3]||(s[3]=y=>a.value=y),onInput:f,class:"range-input"},null,544),[[te,a.value]]),e("span",bt,Z(a.value),1)])]),e("div",wt,[s[18]||(s[18]=e("h3",null,"相机设置",-1)),e("div",yt,[s[17]||(s[17]=e("label",null,"视野角度",-1)),q(e("input",{type:"range",min:"10",max:"120",step:"5","onUpdate:modelValue":s[4]||(s[4]=y=>m.value=y),onInput:$,class:"range-input"},null,544),[[te,m.value]]),e("span",Lt,Z(m.value)+"°",1)]),e("button",{class:"btn btn-primary",onClick:A}," 重置相机位置 ")])])])],512)),[[ce,P.visible]])}}),Ct=K(Mt,[["__scopeId","data-v-04eb5015"]]),kt={class:"play-controls"},At={class:"time-display"},Vt={class:"current-time"},St={class:"total-time"},Et={class:"progress-track"},Ht={class:"control-buttons"},xt={key:0,class:"icon",viewBox:"0 0 24 24"},$t={key:1,class:"icon",viewBox:"0 0 24 24"},Pt={class:"speed-control"},Dt={class:"volume-control"},Tt={key:0,class:"icon",viewBox:"0 0 24 24"},_t={key:1,class:"icon",viewBox:"0 0 24 24"},It={key:2,class:"icon",viewBox:"0 0 24 24"},Ft={class:"volume-slider"},Rt=Q({__name:"PlayControls",props:{playing:{type:Boolean},progress:{},duration:{}},emits:["update:playing","update:progress","play","pause","stop","seek"],setup(o,{emit:t}){const n=o,i=t,r=d(),a=d(!1),m=d("1"),v=d(1),p=d(!1),b=d(!1),C=d(1),E=ee(()=>n.progress),f=ee(()=>n.duration===0?0:n.progress/n.duration*100),$=u=>{const c=Math.floor(u/60),H=Math.floor(u%60);return`${c.toString().padStart(2,"0")}:${H.toString().padStart(2,"0")}`},A=()=>{n.playing?i("pause"):i("play"),i("update:playing",!n.playing)},P=()=>{i("seek",0),i("update:progress",0),n.playing||(i("play"),i("update:playing",!0))},s=u=>{if(!r.value||a.value)return;const c=r.value.getBoundingClientRect(),x=(u.clientX-c.left)/c.width*n.duration;i("seek",x),i("update:progress",x)},y=u=>{a.value=!0;const c=x=>{if(!r.value||!a.value)return;const g=r.value.getBoundingClientRect(),M="touches"in x?x.touches[0].clientX:x.clientX,L=Math.max(0,Math.min(1,(M-g.left)/g.width))*n.duration;i("seek",L),i("update:progress",L)},H=()=>{a.value=!1,document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",H),document.removeEventListener("touchmove",c),document.removeEventListener("touchend",H)};document.addEventListener("mousemove",c),document.addEventListener("mouseup",H),document.addEventListener("touchmove",c),document.addEventListener("touchend",H),u.preventDefault()},S=()=>{},k=()=>{p.value?(v.value=C.value,p.value=!1):(C.value=v.value,v.value=0,p.value=!0),h()},h=()=>{p.value=v.value===0};return W(()=>{const u=c=>{if(c.target===document.body)switch(c.code){case"Space":c.preventDefault(),A();break;case"ArrowLeft":c.preventDefault(),i("seek",Math.max(0,n.progress-5));break;case"ArrowRight":c.preventDefault(),i("seek",Math.min(n.duration,n.progress+5));break}};document.addEventListener("keydown",u),J(()=>{document.removeEventListener("keydown",u)})}),(u,c)=>(_(),I("div",kt,[e("div",At,[e("span",Vt,Z($(E.value)),1),c[3]||(c[3]=e("span",{class:"separator"},"/",-1)),e("span",St,Z($(u.duration)),1)]),e("div",{class:"progress-container",onClick:s,ref_key:"progressContainer",ref:r},[e("div",Et,[e("div",{class:"progress-fill",style:le({width:f.value+"%"})},null,4),e("div",{class:"progress-thumb",style:le({left:f.value+"%"}),onMousedown:y,onTouchstart:y},null,36)])],512),e("div",Ht,[e("button",{class:"btn control-btn",onClick:c[0]||(c[0]=H=>u.$emit("stop"))},c[4]||(c[4]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M6,6H18V18H6V6Z"})],-1)])),e("button",{class:"btn control-btn play-btn",onClick:A},[u.playing?(_(),I("svg",$t,c[6]||(c[6]=[e("path",{d:"M14,19H18V5H14M6,19H10V5H6V19Z"},null,-1)]))):(_(),I("svg",xt,c[5]||(c[5]=[e("path",{d:"M8,5.14V19.14L19,12.14L8,5.14Z"},null,-1)])))]),e("button",{class:"btn control-btn",onClick:P},c[7]||(c[7]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M12,5V1L7,6L12,11V7A6,6 0 0,1 18,13A6,6 0 0,1 12,19A6,6 0 0,1 6,13H4A8,8 0 0,0 12,21A8,8 0 0,0 20,13A8,8 0 0,0 12,5Z"})],-1)])),e("div",Pt,[q(e("select",{"onUpdate:modelValue":c[1]||(c[1]=H=>m.value=H),onChange:S,class:"speed-select"},c[8]||(c[8]=[oe('<option value="0.5" data-v-7b9a1cec>0.5x</option><option value="0.75" data-v-7b9a1cec>0.75x</option><option value="1" data-v-7b9a1cec>1x</option><option value="1.25" data-v-7b9a1cec>1.25x</option><option value="1.5" data-v-7b9a1cec>1.5x</option><option value="2" data-v-7b9a1cec>2x</option>',6)]),544),[[ie,m.value]])]),e("div",Dt,[e("button",{class:"btn control-btn",onClick:k},[!p.value&&v.value>.5?(_(),I("svg",Tt,c[9]||(c[9]=[e("path",{d:"M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.85 14,18.71V20.77C18.01,19.86 21,16.28 21,12C21,7.72 18.01,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z"},null,-1)]))):!p.value&&v.value>0?(_(),I("svg",_t,c[10]||(c[10]=[e("path",{d:"M5,9V15H9L14,20V4L9,9M18.5,12C18.5,10.23 17.5,8.71 16,7.97V16C17.5,15.29 18.5,13.76 18.5,12Z"},null,-1)]))):(_(),I("svg",It,c[11]||(c[11]=[e("path",{d:"M12,4L9.91,6.09L12,8.18M4.27,3L3,4.27L7.73,9H3V15H7L12,20V13.27L16.25,17.53C15.58,18.04 14.83,18.46 14,18.7V20.77C15.38,20.45 16.63,19.82 17.68,18.96L19.73,21L21,19.73L12,10.73M19,12C19,12.94 18.8,13.82 18.46,14.64L19.97,16.15C20.62,14.91 21,13.5 21,12C21,7.72 18,4.14 14,3.23V5.29C16.89,6.15 19,8.83 19,12M16.5,12C16.5,10.23 15.5,8.71 14,7.97V10.18L16.45,12.63C16.5,12.43 16.5,12.21 16.5,12Z"},null,-1)])))]),q(e("div",Ft,[q(e("input",{type:"range",min:"0",max:"1",step:"0.1","onUpdate:modelValue":c[2]||(c[2]=H=>v.value=H),onInput:h,class:"volume-input"},null,544),[[te,v.value]])],512),[[ce,b.value]])])])]))}}),zt=K(Rt,[["__scopeId","data-v-7b9a1cec"]]),Bt={class:"transform-controls"},Ut={class:"mode-buttons"},Ot={key:0,class:"gesture-hints mobile-only"},Zt=Q({__name:"TransformControls",props:{mode:{}},emits:["update:mode"],setup(o){const t=d(!1),n=()=>{},i=()=>{},r=()=>{},a=()=>/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),m=()=>{t.value=!t.value,t.value&&setTimeout(()=>{t.value=!1},3e3)};return W(()=>{a()&&setTimeout(()=>{t.value=!0,setTimeout(()=>{t.value=!1},5e3)},1e3);const v=p=>{if(p.target===document.body)switch(p.key.toLowerCase()){case"g":p.preventDefault();break;case"z":p.preventDefault();break;case"r":p.preventDefault();break;case"h":p.preventDefault(),m();break}};document.addEventListener("keydown",v),J(()=>{document.removeEventListener("keydown",v)})}),(v,p)=>(_(),I("div",Bt,[e("div",Ut,[e("button",{class:ne(["btn mode-btn",{active:v.mode==="translate"}]),onClick:p[0]||(p[0]=b=>v.$emit("update:mode","translate")),title:"移动模式"},p[3]||(p[3]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M13,1L11,1L11,7L7,7L7,9L11,9L11,15L7,15L7,17L11,17L11,23L13,23L13,17L17,17L17,15L13,15L13,9L17,9L17,7L13,7L13,1Z"})],-1)]),2),e("button",{class:ne(["btn mode-btn",{active:v.mode==="rotate"}]),onClick:p[1]||(p[1]=b=>v.$emit("update:mode","rotate")),title:"旋转模式"},p[4]||(p[4]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12H16A4,4 0 0,0 12,8V6Z"})],-1)]),2),e("button",{class:ne(["btn mode-btn",{active:v.mode==="scale"}]),onClick:p[2]||(p[2]=b=>v.$emit("update:mode","scale")),title:"缩放模式"},p[5]||(p[5]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M11,13H5V11H11V5H13V11H19V13H13V19H11V13Z"})],-1)]),2)]),t.value?(_(),I("div",Ot,p[6]||(p[6]=[oe('<div class="hint-item" data-v-f3bb236a><div class="gesture-icon" data-v-f3bb236a>👆</div><span data-v-f3bb236a>单指拖拽：旋转视角</span></div><div class="hint-item" data-v-f3bb236a><div class="gesture-icon" data-v-f3bb236a>✌️</div><span data-v-f3bb236a>双指捏合：缩放</span></div><div class="hint-item" data-v-f3bb236a><div class="gesture-icon" data-v-f3bb236a>✋</div><span data-v-f3bb236a>双指拖拽：平移</span></div>',3)]))):N("",!0),e("div",{class:"quick-actions"},[e("button",{class:"btn action-btn",onClick:n,title:"重置视角"},p[7]||(p[7]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M13,7L12,8L11,7H13M7,11L8,12L7,13V11M17,11V13L16,12L17,11M11,17H13L12,16L11,17Z"})],-1)])),e("button",{class:"btn action-btn",onClick:i,title:"线框模式"},p[8]||(p[8]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M12,2L1,21H23M12,6L19.53,19H4.47"})],-1)])),e("button",{class:"btn action-btn",onClick:r,title:"显示网格"},p[9]||(p[9]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M10,4V8H14V4H16V8H20V10H16V14H20V16H16V20H14V16H10V20H8V16H4V14H8V10H4V8H8V4H10M10,10V14H14V10H10Z"})],-1)]))])]))}}),Xt=K(Zt,[["__scopeId","data-v-f3bb236a"]]),qt={class:"dialog-header"},Gt={class:"dialog-footer"},jt=Q({__name:"HelpDialog",props:{visible:{type:Boolean}},emits:["update:visible"],setup(o){return(t,n)=>q((_(),I("div",{class:"help-overlay",onClick:n[3]||(n[3]=i=>t.$emit("update:visible",!1))},[e("div",{class:"help-dialog",onClick:n[2]||(n[2]=he(()=>{},["stop"]))},[e("div",qt,[n[5]||(n[5]=e("h2",null,"使用帮助",-1)),e("button",{class:"btn close-btn",onClick:n[0]||(n[0]=i=>t.$emit("update:visible",!1))},n[4]||(n[4]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})],-1)]))]),n[6]||(n[6]=oe('<div class="dialog-content" data-v-00cf7c92><div class="help-section" data-v-00cf7c92><h3 data-v-00cf7c92>📁 文件加载</h3><ul data-v-00cf7c92><li data-v-00cf7c92><strong data-v-00cf7c92>模型文件：</strong>支持 .pmd 和 .pmx 格式的MMD模型文件</li><li data-v-00cf7c92><strong data-v-00cf7c92>动作文件：</strong>支持 .vmd 格式的MMD动作文件</li><li data-v-00cf7c92><strong data-v-00cf7c92>音乐文件：</strong>支持 .mp3、.wav、.ogg 格式的音频文件</li></ul></div><div class="help-section" data-v-00cf7c92><h3 data-v-00cf7c92>🎮 控制操作</h3><div class="controls-grid" data-v-00cf7c92><div class="control-item" data-v-00cf7c92><div class="control-type" data-v-00cf7c92>鼠标控制</div><ul data-v-00cf7c92><li data-v-00cf7c92>左键拖拽：旋转视角</li><li data-v-00cf7c92>右键拖拽：平移视角</li><li data-v-00cf7c92>滚轮：缩放视角</li></ul></div><div class="control-item" data-v-00cf7c92><div class="control-type" data-v-00cf7c92>触摸控制</div><ul data-v-00cf7c92><li data-v-00cf7c92>单指拖拽：旋转视角</li><li data-v-00cf7c92>双指拖拽：平移视角</li><li data-v-00cf7c92>双指捏合：缩放视角</li></ul></div></div></div><div class="help-section" data-v-00cf7c92><h3 data-v-00cf7c92>⌨️ 键盘快捷键</h3><div class="shortcuts-grid" data-v-00cf7c92><div class="shortcut-item" data-v-00cf7c92><kbd data-v-00cf7c92>空格</kbd><span data-v-00cf7c92>播放/暂停</span></div><div class="shortcut-item" data-v-00cf7c92><kbd data-v-00cf7c92>←</kbd><span data-v-00cf7c92>后退5秒</span></div><div class="shortcut-item" data-v-00cf7c92><kbd data-v-00cf7c92>→</kbd><span data-v-00cf7c92>前进5秒</span></div><div class="shortcut-item" data-v-00cf7c92><kbd data-v-00cf7c92>R</kbd><span data-v-00cf7c92>重置视角</span></div><div class="shortcut-item" data-v-00cf7c92><kbd data-v-00cf7c92>G</kbd><span data-v-00cf7c92>显示/隐藏网格</span></div><div class="shortcut-item" data-v-00cf7c92><kbd data-v-00cf7c92>Z</kbd><span data-v-00cf7c92>线框模式</span></div><div class="shortcut-item" data-v-00cf7c92><kbd data-v-00cf7c92>H</kbd><span data-v-00cf7c92>显示/隐藏手势提示</span></div></div></div><div class="help-section" data-v-00cf7c92><h3 data-v-00cf7c92>🎵 播放控制</h3><ul data-v-00cf7c92><li data-v-00cf7c92>使用底部播放控制条控制动画和音乐播放</li><li data-v-00cf7c92>可以拖拽进度条跳转到指定时间</li><li data-v-00cf7c92>支持调节播放速度和音量</li><li data-v-00cf7c92>音乐和动画会自动同步播放</li></ul></div><div class="help-section" data-v-00cf7c92><h3 data-v-00cf7c92>⚙️ 场景设置</h3><ul data-v-00cf7c92><li data-v-00cf7c92>在侧边栏中可以调整背景颜色</li><li data-v-00cf7c92>可以调节环境光和主光源的强度</li><li data-v-00cf7c92>可以调整相机的视野角度</li><li data-v-00cf7c92>支持重置相机位置到默认状态</li></ul></div><div class="help-section" data-v-00cf7c92><h3 data-v-00cf7c92>📱 移动端优化</h3><ul data-v-00cf7c92><li data-v-00cf7c92>界面针对触摸操作进行了优化</li><li data-v-00cf7c92>支持手势控制和触摸友好的按钮大小</li><li data-v-00cf7c92>自动适配不同屏幕尺寸</li><li data-v-00cf7c92>支持全屏模式获得更好的观看体验</li></ul></div><div class="help-section" data-v-00cf7c92><h3 data-v-00cf7c92>💡 使用提示</h3><ul data-v-00cf7c92><li data-v-00cf7c92>建议在加载大型模型文件时保持网络稳定</li><li data-v-00cf7c92>如果遇到性能问题，可以尝试降低画质设置</li><li data-v-00cf7c92>支持同时加载多个动作文件进行切换</li><li data-v-00cf7c92>可以保存当前场景设置为默认配置</li></ul></div></div>',1)),e("div",Gt,[e("button",{class:"btn btn-primary",onClick:n[1]||(n[1]=i=>t.$emit("update:visible",!1))}," 我知道了 ")])])],512)),[[ce,t.visible]])}}),Wt=K(jt,[["__scopeId","data-v-00cf7c92"]]),Yt={class:"loading-overlay"},Nt={class:"loading-content"},Qt={class:"loading-text"},Jt={key:0,class:"progress-text"},Kt={key:0,class:"progress-bar"},en={class:"loading-tips"},tn={class:"tip-text"},nn=Q({__name:"LoadingOverlay",props:{message:{},progress:{}},setup(o){const t=d(""),n=d(),i=["💡 您可以使用鼠标或触摸手势来控制视角","🎵 支持同时播放音乐和动画，会自动同步","⌨️ 按空格键可以快速播放/暂停动画","📱 界面已针对移动设备进行优化","🎮 支持多种文件格式：PMD、PMX、VMD","⚙️ 可以在侧边栏调整场景和灯光设置","🔄 按R键可以快速重置相机位置","📏 支持线框模式和网格显示","🎯 双击可以快速聚焦到模型","💾 场景设置会自动保存到本地"],r=()=>{const a=Math.floor(Math.random()*i.length);t.value=i[a]};return W(()=>{r(),n.value=window.setInterval(()=>{r()},3e3)}),J(()=>{n.value&&clearInterval(n.value)}),(a,m)=>(_(),I("div",Yt,[e("div",Nt,[m[0]||(m[0]=e("div",{class:"loading-spinner"},[e("div",{class:"spinner-ring"}),e("div",{class:"spinner-ring"}),e("div",{class:"spinner-ring"})],-1)),e("div",Qt,[e("h3",null,Z(a.message||"正在加载..."),1),a.progress!==void 0?(_(),I("p",Jt,Z(Math.round(a.progress*100))+"% ",1)):N("",!0)]),a.progress!==void 0?(_(),I("div",Kt,[e("div",{class:"progress-fill",style:le({width:a.progress*100+"%"})},null,4)])):N("",!0),e("div",en,[e("p",tn,Z(t.value),1)])])]))}}),an=K(nn,[["__scopeId","data-v-bb38eb20"]]);function on(){const o=d(window.innerWidth),t=d(window.innerHeight),n=d(!1),i=d(!1),r=d(!1),a=d("portrait"),m=d(window.devicePixelRatio||1),v={mobile:768,tablet:1024,desktop:1200},p=()=>{const h=o.value;n.value=h<v.mobile,i.value=h>=v.mobile&&h<v.desktop,r.value=h>=v.desktop,a.value=o.value>t.value?"landscape":"portrait"},b=()=>{o.value=window.innerWidth,t.value=window.innerHeight,m.value=window.devicePixelRatio||1,p()},C=()=>{setTimeout(()=>{b()},100)},E=()=>"ontouchstart"in window||navigator.maxTouchPoints>0,f=()=>/iPad|iPhone|iPod/.test(navigator.userAgent),$=()=>/Android/.test(navigator.userAgent),A=()=>{const h=getComputedStyle(document.documentElement);return{top:parseInt(h.getPropertyValue("--sat")||"0"),right:parseInt(h.getPropertyValue("--sar")||"0"),bottom:parseInt(h.getPropertyValue("--sab")||"0"),left:parseInt(h.getPropertyValue("--sal")||"0")}},P=()=>{let h=document.querySelector('meta[name="viewport"]');h||(h=document.createElement("meta"),h.setAttribute("name","viewport"),document.head.appendChild(h));const u=["width=device-width","initial-scale=1.0","maximum-scale=1.0","user-scalable=no","viewport-fit=cover"].join(", ");h.setAttribute("content",u)},s=()=>{let h=0;const u=c=>{const H=Date.now();H-h<=300&&c.preventDefault(),h=H};return document.addEventListener("touchend",u,{passive:!1}),()=>{document.removeEventListener("touchend",u)}},y=()=>{if(!f())return;const h=()=>{const u=window.innerHeight*.01;document.documentElement.style.setProperty("--vh",`${u}px`)};return h(),window.addEventListener("resize",h),window.addEventListener("orientationchange",()=>{setTimeout(h,100)}),()=>{window.removeEventListener("resize",h),window.removeEventListener("orientationchange",h)}},S=()=>({isMobile:n.value,isTablet:i.value,isDesktop:r.value,isTouchDevice:E(),isIOS:f(),isAndroid:$(),orientation:a.value,windowWidth:o.value,windowHeight:t.value,devicePixelRatio:m.value,safeAreaInsets:A()}),k=()=>{const h=n.value?2073600:8294400;o.value/t.value;let u=o.value*m.value,c=t.value*m.value;const H=u*c;if(H>h){const x=Math.sqrt(h/H);u*=x,c*=x}return{width:Math.floor(u),height:Math.floor(c),pixelRatio:Math.min(m.value,2)}};return W(()=>{p(),P();const h=s(),u=y();window.addEventListener("resize",b),window.addEventListener("orientationchange",C),J(()=>{window.removeEventListener("resize",b),window.removeEventListener("orientationchange",C),h==null||h(),u==null||u()})}),{windowWidth:o,windowHeight:t,isMobile:n,isTablet:i,isDesktop:r,orientation:a,devicePixelRatio:m,getDeviceInfo:S,getOptimalCanvasSize:k,getSafeAreaInsets:A,isTouchDevice:E,isIOS:f,isAndroid:$,breakpoints:v}}function sn(){const o=d({fps:0,frameTime:0,memoryUsage:0,renderTime:0,triangleCount:0,drawCalls:0}),t=d(!1),n=d("high");let i=0,r=performance.now(),a=0,m=null,v=null;const p=()=>{const u=performance.now(),c=u-r;a+=c,i++,i>=60&&(o.value.fps=Math.round(1e3/(a/i)),o.value.frameTime=a/i,i=0,a=0),r=u},b=()=>{if("memory"in performance){const u=performance.memory;o.value.memoryUsage=Math.round(u.usedJSHeapSize/1024/1024)}},C=()=>{const{fps:u,memoryUsage:c}=o.value;u>=50&&c<100?n.value="high":u>=30&&c<200?n.value="medium":n.value="low"},E=()=>{const u=[];return o.value.fps<30&&(u.push("降低渲染质量以提高帧率"),u.push("减少场景中的模型数量"),u.push("关闭阴影和后处理效果")),o.value.memoryUsage>150&&(u.push("清理未使用的纹理和几何体"),u.push("使用较低分辨率的纹理"),u.push("减少同时加载的模型数量")),o.value.drawCalls>100&&(u.push("合并几何体以减少绘制调用"),u.push("使用实例化渲染")),u},f=()=>{const u=n.value;return{pixelRatio:u==="high"?2:u==="medium"?1.5:1,shadowMapSize:u==="high"?2048:u==="medium"?1024:512,antialias:u==="high",enableShadows:u!=="low",maxLights:u==="high"?8:u==="medium"?4:2,lodDistance:u==="high"?100:u==="medium"?50:25}},$=()=>{t.value&&(p(),v=requestAnimationFrame($))},A=()=>{t.value||(t.value=!0,r=performance.now(),i=0,a=0,$(),m=window.setInterval(()=>{b(),C()},1e3))},P=()=>{t.value=!1,v&&(cancelAnimationFrame(v),v=null),m&&(clearInterval(m),m=null)},s=()=>{o.value={fps:0,frameTime:0,memoryUsage:0,renderTime:0,triangleCount:0,drawCalls:0},i=0,a=0},y=(u,c,H)=>{o.value.renderTime=u,o.value.triangleCount=c,o.value.drawCalls=H},S=u=>{const c=performance.now();return{end:()=>performance.now()-c}},k=()=>o.value.memoryUsage>100?{shouldCleanup:!0,urgency:o.value.memoryUsage>200?"high":"medium",suggestions:["清理未使用的纹理","释放几何体缓冲区","清空动画缓存","重新加载场景"]}:{shouldCleanup:!1,urgency:"low",suggestions:[]},h=()=>{const u=document.createElement("canvas"),c=u.getContext("webgl2")||u.getContext("webgl");return c?{webgl:!0,webgl2:!!u.getContext("webgl2"),maxTextureSize:c.getParameter(c.MAX_TEXTURE_SIZE),maxVertexAttribs:c.getParameter(c.MAX_VERTEX_ATTRIBS),maxFragmentUniforms:c.getParameter(c.MAX_FRAGMENT_UNIFORM_VECTORS),renderer:c.getParameter(c.RENDERER),vendor:c.getParameter(c.VENDOR)}:{webgl:!1,webgl2:!1,maxTextureSize:0,maxVertexAttribs:0,maxFragmentUniforms:0}};return W(()=>{A()}),J(()=>{P()}),{metrics:o,isMonitoring:t,performanceLevel:n,startMonitoring:A,stopMonitoring:P,resetMetrics:s,updateRenderMetrics:y,getPerformanceRecommendations:E,autoOptimize:f,createProfiler:S,suggestMemoryCleanup:k,detectDeviceCapabilities:h}}const ln={key:0,class:"mobile-controls"},rn={class:"fab-container"},un={class:"icon",viewBox:"0 0 24 24"},cn={key:0,d:"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"},dn={key:1,d:"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"},vn={key:0,class:"fab-menu"},mn={class:"icon",viewBox:"0 0 24 24"},pn={key:0,d:"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"},fn={key:1,d:"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"},gn={key:0,class:"gesture-indicator"},hn={key:0,class:"quick-settings"},bn={class:"quick-settings-header"},wn={class:"quick-settings-content"},yn={class:"setting-item"},Ln={class:"setting-item"},Mn={class:"setting-item"},Cn={class:"value"},kn={class:"setting-item"},An={key:1,class:"performance-monitor"},Vn={class:"perf-item"},Sn={class:"perf-item"},En={class:"value"},Hn=Q({__name:"MobileControls",emits:["reset-camera","take-screenshot","quality-change","fps-limit-change","sensitivity-change"],setup(o,{emit:t}){const{isMobile:n}=on(),{metrics:i}=sn(),r=d(!1),a=d(!1),m=d(!1),v=d(!1),p=d(!1),b=d("medium"),C=d("60"),E=d(1),f=d(!0),$=d(0),A=d(0),P=t,s=()=>{r.value=!r.value,f.value&&g(50)},y=()=>{P("reset-camera"),r.value=!1,f.value&&g(100)},S=()=>{document.fullscreenElement?(document.exitFullscreen(),p.value=!1):(document.documentElement.requestFullscreen(),p.value=!0),r.value=!1},k=()=>{P("take-screenshot"),r.value=!1,f.value&&g(200)},h=()=>{m.value=!0,r.value=!1},u=()=>{P("quality-change",b.value)},c=()=>{P("fps-limit-change",parseInt(C.value))},H=()=>{P("sensitivity-change",E.value)},x=()=>{f.value=!f.value,f.value&&g(100)},g=R=>{"vibrate"in navigator&&f.value&&navigator.vibrate(R)},M=R=>R>=50?"good":R>=30?"medium":"poor",l=()=>{a.value=!0,setTimeout(()=>{a.value=!1},3e3)},L=()=>{p.value=!!document.fullscreenElement},T=()=>{$.value=i.value.fps,A.value=i.value.memoryUsage};return W(()=>{document.addEventListener("fullscreenchange",L),!localStorage.getItem("hasSeenGestureHint")&&n.value&&(setTimeout(l,2e3),localStorage.setItem("hasSeenGestureHint","true"));const w=setInterval(T,1e3);J(()=>{document.removeEventListener("fullscreenchange",L),clearInterval(w)})}),(R,w)=>j(n)?(_(),I("div",ln,[e("div",rn,[e("button",{class:ne(["fab main-fab",{active:r.value}]),onClick:s},[(_(),I("svg",un,[r.value?(_(),I("path",dn)):(_(),I("path",cn))]))],2),Y(me,{name:"fab-menu"},{default:pe(()=>[r.value?(_(),I("div",vn,[e("button",{class:"fab sub-fab",onClick:y,title:"重置视角"},w[4]||(w[4]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M13,7L12,8L11,7H13M7,11L8,12L7,13V11M17,11V13L16,12L17,11M11,17H13L12,16L11,17Z"})],-1)])),e("button",{class:"fab sub-fab",onClick:S,title:"全屏"},[(_(),I("svg",mn,[p.value?(_(),I("path",fn)):(_(),I("path",pn))]))]),e("button",{class:"fab sub-fab",onClick:k,title:"截图"},w[5]||(w[5]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z"})],-1)])),e("button",{class:"fab sub-fab",onClick:h,title:"设置"},w[6]||(w[6]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"})],-1)]))])):N("",!0)]),_:1})]),a.value?(_(),I("div",gn,w[7]||(w[7]=[oe('<div class="gesture-hint" data-v-8d1f4a3e><div class="gesture-icon" data-v-8d1f4a3e><svg viewBox="0 0 100 100" data-v-8d1f4a3e><circle cx="30" cy="50" r="8" class="touch-point" data-v-8d1f4a3e></circle><circle cx="70" cy="50" r="8" class="touch-point" data-v-8d1f4a3e></circle><path d="M30,50 Q50,30 70,50" stroke="currentColor" stroke-width="2" fill="none" class="gesture-path" data-v-8d1f4a3e></path></svg></div><span class="gesture-text" data-v-8d1f4a3e>双指捏合缩放</span></div>',1)]))):N("",!0),Y(me,{name:"slide-up"},{default:pe(()=>[m.value?(_(),I("div",hn,[e("div",bn,[w[9]||(w[9]=e("h3",null,"快速设置",-1)),e("button",{class:"close-btn",onClick:w[0]||(w[0]=B=>m.value=!1)},w[8]||(w[8]=[e("svg",{class:"icon",viewBox:"0 0 24 24"},[e("path",{d:"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"})],-1)]))]),e("div",wn,[e("div",yn,[w[11]||(w[11]=e("label",null,"画质",-1)),q(e("select",{"onUpdate:modelValue":w[1]||(w[1]=B=>b.value=B),onChange:u},w[10]||(w[10]=[e("option",{value:"low"},"低",-1),e("option",{value:"medium"},"中",-1),e("option",{value:"high"},"高",-1)]),544),[[ie,b.value]])]),e("div",Ln,[w[13]||(w[13]=e("label",null,"帧率限制",-1)),q(e("select",{"onUpdate:modelValue":w[2]||(w[2]=B=>C.value=B),onChange:c},w[12]||(w[12]=[e("option",{value:"30"},"30 FPS",-1),e("option",{value:"60"},"60 FPS",-1),e("option",{value:"0"},"无限制",-1)]),544),[[ie,C.value]])]),e("div",Mn,[w[14]||(w[14]=e("label",null,"触摸灵敏度",-1)),q(e("input",{type:"range",min:"0.5",max:"2",step:"0.1","onUpdate:modelValue":w[3]||(w[3]=B=>E.value=B),onInput:H},null,544),[[te,E.value]]),e("span",Cn,Z(E.value)+"x",1)]),e("div",kn,[w[15]||(w[15]=e("label",null,"震动反馈",-1)),e("button",{class:ne(["toggle-btn",{active:f.value}]),onClick:x},Z(f.value?"开启":"关闭"),3)])])])):N("",!0)]),_:1}),v.value?(_(),I("div",An,[e("div",Vn,[w[16]||(w[16]=e("span",{class:"label"},"FPS:",-1)),e("span",{class:ne(["value",M($.value)])},Z($.value),3)]),e("div",Sn,[w[17]||(w[17]=e("span",{class:"label"},"内存:",-1)),e("span",En,Z(A.value)+"MB",1)])])):N("",!0)])):N("",!0)}}),xn=K(Hn,[["__scopeId","data-v-8d1f4a3e"]]),$n={class:"mmd-viewer"},Pn=Q({__name:"MMDViewer",setup(o){const t=We(),n=d(),i=d(!1),r=d("translate"),{scene:a,camera:m,renderer:v,initScene:p,loadModel:b,loadMotion:C,loadMusic:E,cleanup:f}=qe(),{play:$,pause:A,stop:P,seek:s,getCurrentTime:y,setAudio:S}=Ge();let k=null;const h=()=>{t.toggleSidebar()},u=async F=>{t.showLoading("正在加载模型...");try{const D=await b(F);t.addModel({id:Date.now().toString(),name:F.name,object:D,animations:[]})}catch{alert("模型加载失败，请检查文件格式是否正确")}finally{t.hideLoading()}},c=async F=>{t.showLoading("正在加载动作...");try{const D=await C(F);t.addMotion({id:Date.now().toString(),name:F.name,duration:D.duration||10,data:D})}catch{alert("动作加载失败，请检查文件格式是否正确")}finally{t.hideLoading()}},H=async F=>{t.showLoading("正在加载音乐...");try{const D=await E(F),U={id:Date.now().toString(),name:F.name,element:D,duration:D.duration||0};t.addAudio(U),S(D)}catch{alert("音乐加载失败，请检查文件格式是否正确")}finally{t.hideLoading()}},x=()=>{$(),t.play()},g=()=>{A(),t.pause()},M=()=>{P(),t.stop()},l=F=>{s(F),t.seek(F)},L=()=>{k&&k.reset()},T=()=>{if(v.value){const F=v.value.domElement,D=document.createElement("a");D.download=`mmd-screenshot-${Date.now()}.png`,D.href=F.toDataURL("image/png"),D.click()}},R=F=>{if(v.value){const D=F==="high"?2:F==="medium"?1.5:1;v.value.setPixelRatio(Math.min(D,window.devicePixelRatio))}},w=F=>{},B=F=>{k&&(k.settings.rotateSpeed=F,k.settings.zoomSpeed=F*2,k.settings.panSpeed=F)};return Me(()=>t.sceneSettings,F=>{a.value&&(a.value.background=new be(F.backgroundColor))},{deep:!0}),W(async()=>{n.value&&(await p(n.value),m.value&&v.value&&(k=je(m.value,v.value)));const F=()=>{t.isPlaying&&(t.currentTime=y()),k&&k.update(),requestAnimationFrame(F)};F()}),J(()=>{f()}),(F,D)=>(_(),I("div",$n,[e("div",{ref_key:"sceneContainer",ref:n,class:"scene-container"},null,512),Y(at,{onToggleSidebar:h,onShowHelp:D[0]||(D[0]=U=>i.value=!0)}),Y(Ct,{visible:j(t).sidebarVisible,"onUpdate:visible":D[1]||(D[1]=U=>j(t).sidebarVisible=U),onLoadModel:u,onLoadMotion:c,onLoadMusic:H},null,8,["visible"]),Y(zt,{playing:j(t).isPlaying,"onUpdate:playing":D[2]||(D[2]=U=>j(t).isPlaying=U),progress:j(t).currentTime,"onUpdate:progress":D[3]||(D[3]=U=>j(t).currentTime=U),duration:j(t).totalDuration,onPlay:x,onPause:g,onStop:M,onSeek:l},null,8,["playing","progress","duration"]),Y(Xt,{mode:r.value,"onUpdate:mode":D[4]||(D[4]=U=>r.value=U),class:"transform-controls"},null,8,["mode"]),Y(Wt,{visible:i.value,"onUpdate:visible":D[5]||(D[5]=U=>i.value=U)},null,8,["visible"]),j(t).loading?(_(),Ce(an,{key:0,message:j(t).loadingMessage},null,8,["message"])):N("",!0),Y(xn,{onResetCamera:L,onTakeScreenshot:T,onQualityChange:R,onFpsLimitChange:w,onSensitivityChange:B})]))}}),Dn=K(Pn,[["__scopeId","data-v-0bcfaad8"]]),Tn=ke({history:Ae("/"),routes:[{path:"/",name:"home",component:Dn},{path:"/about",name:"about",component:()=>Xe(()=>import("./AboutView-d002Q_8Y.js"),__vite__mapDeps([0,1,2,3,4]))}]});function _n(){const o=d([]),t=d([]),n=d(!0),i=d(1e3),r=d(50),a=Ve({total:0,byType:{error:0,warning:0,info:0},byComponent:{}}),m=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),v=(g,M,l,L,T)=>{const R={id:m(),type:g,message:M,details:l,timestamp:Date.now(),stack:new Error().stack,component:L,action:T};return o.value.unshift(R),o.value.length>r.value&&(o.value=o.value.slice(0,r.value)),a.total++,a.byType[g]++,L&&(a.byComponent[L]=(a.byComponent[L]||0)+1),p(g==="error"?"error":g==="warning"?"warn":"info",M,{details:l,component:L,action:T}),R},p=(g,M,l,L)=>{if(!n.value)return;const T={id:m(),level:g,message:M,data:l,timestamp:Date.now(),component:L};return t.value.unshift(T),t.value.length>i.value&&(t.value=t.value.slice(0,i.value)),T},b=(g,M,l)=>p("debug",g,M,l),C=(g,M,l)=>p("info",g,M,l),E=(g,M,l)=>p("warn",g,M,l),f=(g,M,l)=>p("error",g,M,l),$=(g,M,l,L)=>v("error",g,M,l,L),A=(g,M,l,L)=>v("warning",g,M,l,L),P=(g,M,l,L)=>v("info",g,M,l,L),s=()=>{o.value=[],a.total=0,a.byType={error:0,warning:0,info:0},a.byComponent={}},y=()=>{t.value=[]},S=g=>{const M=o.value.findIndex(l=>l.id===g);if(M!==-1){const l=o.value[M];o.value.splice(M,1),a.total--,a.byType[l.type]--,l.component&&(a.byComponent[l.component]--,a.byComponent[l.component]<=0&&delete a.byComponent[l.component])}},k=(g,M)=>o.value.filter(l=>!(g&&l.type!==g||M&&l.component!==M)),h=(g,M)=>t.value.filter(l=>!(g&&l.level!==g||M&&l.component!==M)),u=()=>{const g={timestamp:new Date().toISOString(),stats:a,errors:o.value,logs:t.value.slice(0,100),userAgent:navigator.userAgent,url:window.location.href};return JSON.stringify(g,null,2)},c=()=>{window.addEventListener("unhandledrejection",g=>{var M;$("未处理的Promise拒绝",((M=g.reason)==null?void 0:M.toString())||"未知错误","Global","unhandledrejection")}),window.addEventListener("error",g=>{$(g.message||"未知JavaScript错误",`${g.filename}:${g.lineno}:${g.colno}`,"Global","javascript-error")}),window.addEventListener("error",g=>{if(g.target&&g.target!==window){const M=g.target;$("资源加载失败",`${M.tagName}: ${M.src||M.href}`,"Global","resource-error")}},!0)},H=()=>{if("PerformanceObserver"in window)try{new PerformanceObserver(M=>{M.getEntries().forEach(l=>{l.duration>50&&A("检测到长任务",`任务执行时间: ${l.duration.toFixed(2)}ms`,"Performance","long-task")})}).observe({entryTypes:["longtask"]})}catch(g){E("无法启动长任务监控",g,"Performance")}};return{errors:o,logs:t,errorStats:a,isLoggingEnabled:n,maxLogEntries:i,maxErrors:r,error:$,warning:A,info:P,addError:v,log:p,logDebug:b,logInfo:C,logWarn:E,logError:f,clearErrors:s,clearLogs:y,removeError:S,getFilteredErrors:k,getFilteredLogs:h,exportErrorReport:u,init:()=>{c(),H(),C("错误处理系统已初始化",null,"ErrorHandler")}}}const ae=Se(Ue),de=_n();de.init();ae.config.errorHandler=(o,t,n)=>{de.error((o==null?void 0:o.toString())||"未知Vue错误",n,(t==null?void 0:t.$options.name)||"Unknown","vue-error")};ae.config.warnHandler=(o,t,n)=>{de.warning(o,n,(t==null?void 0:t.$options.name)||"Unknown","vue-warning")};ae.use(Ee());ae.use(Tn);ae.mount("#app");export{K as _};
