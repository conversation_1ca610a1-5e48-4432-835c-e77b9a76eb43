import{d as n,e as s,F as f,c,i as t,p as d,t as v,b as l}from"./vue-vendor-BG9NRnXl.js";import{_ as r}from"./index-Bv5dn-cp.js";import"./three-CJ0eQX7f.js";import"./utils-DhSJXSu-.js";const p={class:"about-view"},h={class:"about-container"},u={class:"about-content"},A={class:"version-section"},m={class:"version-info"},M={class:"version-item"},g={class:"value"},b={class:"version-item"},V={class:"value"},_={class:"about-footer"},C=n({__name:"AboutView",setup(D){const i=s(new Date().toLocaleString("zh-CN")),e=s(f);return(o,a)=>(l(),c("div",p,[t("div",h,[a[7]||(a[7]=d('<div class="about-header" data-v-377dfa57><div class="logo" data-v-377dfa57><svg class="logo-icon" viewBox="0 0 24 24" data-v-377dfa57><path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z" data-v-377dfa57></path></svg><h1 data-v-377dfa57>MMD Web查看器</h1></div><p class="subtitle" data-v-377dfa57>移动端优化版 - 专为触摸设备设计</p></div>',1)),t("div",u,[a[5]||(a[5]=d('<section class="info-section" data-v-377dfa57><h2 data-v-377dfa57>关于项目</h2><p data-v-377dfa57> 这是一个基于Web技术开发的MMD（MikuMikuDance）模型查看器， 专门针对移动设备进行了优化。支持加载PMD/PMX模型文件、VMD动作文件， 并提供了丰富的交互控制功能。 </p></section><section class="features-section" data-v-377dfa57><h2 data-v-377dfa57>主要特性</h2><div class="features-grid" data-v-377dfa57><div class="feature-item" data-v-377dfa57><div class="feature-icon" data-v-377dfa57>📱</div><h3 data-v-377dfa57>移动端优化</h3><p data-v-377dfa57>专为触摸设备设计，支持手势控制和响应式布局</p></div><div class="feature-item" data-v-377dfa57><div class="feature-icon" data-v-377dfa57>🎭</div><h3 data-v-377dfa57>模型支持</h3><p data-v-377dfa57>支持PMD和PMX格式的MMD模型文件加载</p></div><div class="feature-item" data-v-377dfa57><div class="feature-icon" data-v-377dfa57>💃</div><h3 data-v-377dfa57>动作播放</h3><p data-v-377dfa57>支持VMD动作文件，可与音乐同步播放</p></div><div class="feature-item" data-v-377dfa57><div class="feature-icon" data-v-377dfa57>🎵</div><h3 data-v-377dfa57>音频同步</h3><p data-v-377dfa57>支持多种音频格式，动画与音乐自动同步</p></div><div class="feature-item" data-v-377dfa57><div class="feature-icon" data-v-377dfa57>🎮</div><h3 data-v-377dfa57>交互控制</h3><p data-v-377dfa57>丰富的相机控制和场景设置选项</p></div><div class="feature-item" data-v-377dfa57><div class="feature-icon" data-v-377dfa57>⚡</div><h3 data-v-377dfa57>高性能</h3><p data-v-377dfa57>基于Three.js和WebGL，提供流畅的3D渲染体验</p></div></div></section><section class="tech-section" data-v-377dfa57><h2 data-v-377dfa57>技术栈</h2><div class="tech-list" data-v-377dfa57><div class="tech-item" data-v-377dfa57><strong data-v-377dfa57>Vue 3</strong><span data-v-377dfa57>现代化的前端框架</span></div><div class="tech-item" data-v-377dfa57><strong data-v-377dfa57>TypeScript</strong><span data-v-377dfa57>类型安全的JavaScript</span></div><div class="tech-item" data-v-377dfa57><strong data-v-377dfa57>Three.js</strong><span data-v-377dfa57>3D图形渲染引擎</span></div><div class="tech-item" data-v-377dfa57><strong data-v-377dfa57>Ammo.js</strong><span data-v-377dfa57>物理引擎支持</span></div><div class="tech-item" data-v-377dfa57><strong data-v-377dfa57>Vite</strong><span data-v-377dfa57>快速的构建工具</span></div><div class="tech-item" data-v-377dfa57><strong data-v-377dfa57>Pinia</strong><span data-v-377dfa57>状态管理</span></div></div></section>',3)),t("section",A,[a[4]||(a[4]=t("h2",null,"版本信息",-1)),t("div",m,[a[3]||(a[3]=t("div",{class:"version-item"},[t("span",{class:"label"},"版本号："),t("span",{class:"value"},"1.0.0")],-1)),t("div",M,[a[1]||(a[1]=t("span",{class:"label"},"构建时间：",-1)),t("span",g,v(i.value),1)]),t("div",b,[a[2]||(a[2]=t("span",{class:"label"},"Vue版本：",-1)),t("span",V,v(e.value),1)])])]),a[6]||(a[6]=d('<section class="contact-section" data-v-377dfa57><h2 data-v-377dfa57>联系方式</h2><p data-v-377dfa57> 如果您在使用过程中遇到问题或有改进建议， 欢迎通过以下方式联系我们： </p><div class="contact-links" data-v-377dfa57><a href="#" class="contact-link" data-v-377dfa57><svg class="icon" viewBox="0 0 24 24" data-v-377dfa57><path d="M12,2A10,10 0 0,1 22,12C22,18.6 17.6,23 11,23C6.4,23 2.5,20.1 1.3,16H8.8L10.4,17.6L15.1,12.9L10.4,8.2L8.8,9.8H1.3C2.5,5.9 6.4,3 11,3A10,10 0 0,1 12,2Z" data-v-377dfa57></path></svg> GitHub </a><a href="#" class="contact-link" data-v-377dfa57><svg class="icon" viewBox="0 0 24 24" data-v-377dfa57><path d="M20,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6A2,2 0 0,0 20,4M20,18H4V8L12,13L20,8V18M20,6L12,11L4,6V6H20V6Z" data-v-377dfa57></path></svg> 邮箱反馈 </a><a href="#" class="contact-link" data-v-377dfa57><svg class="icon" viewBox="0 0 24 24" data-v-377dfa57><path d="M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16Z" data-v-377dfa57></path></svg> 问题反馈 </a></div></section>',1))]),t("div",_,[t("button",{class:"btn btn-primary",onClick:a[0]||(a[0]=L=>o.$router.push("/"))}," 返回主页 ")])])]))}}),x=r(C,[["__scopeId","data-v-377dfa57"]]);export{x as default};
